@props(['title', 'subtitle', 'primaryButtonText' => 'Get Started', 'primaryButtonUrl' => '#', 'secondaryButtonText' => 'Learn More', 'secondaryButtonUrl' => '#'])

<section class="hero-section relative text-white min-h-screen flex items-center">
    <div class="absolute inset-0 bg-black/75"></div>
    <div class="container mx-auto px-2 sm:px-6 relative z-10">
        <div class="grid lg:grid-cols-2 gap-0 lg:gap-12 items-center justify-items-center" class="heross">
                
                
                <div class="mb-[0px] md:mb-[30px] sm:mb-auto text-content order-2 lg:order-1 w-[92%] sm:w-[100%]  flex flex-col items-center lg:items-start text-left lg:text-left md:text-center">
                <h1 class="text-[2.4rem] sm:text-[3.3rem] lg:text-[3.4rem] xl:text-[4.1rem] font-extrabold leading-tight mb-4 sm:mb-6">
                    {{ $title }}
                </h1>
                <p class="text-base sm:text-lg text-gray-300 max-w-xl mb-6 sm:mb-8 lg:mb-10">
                    {{ $subtitle }}
                </p>
                <div class="flex flex-col sm:flex-row gap-3 sm:gap-4 w-full sm:justify-center lg:justify-start hidden sm:flex">
                    <a href="{{ $primaryButtonUrl }}"
                       class="text-left bg-orange-600 px-6 sm:px-8 py-3 sm:py-4 rounded-lg text-base sm:text-lg font-semibold hover:bg-orange-700 transition-colors text-center w-full sm:w-auto">
                        {{ $primaryButtonText }}
                    </a>
                    <a href="{{ $secondaryButtonUrl }}"
                       class="bg-white/10 border border-white/20 px-6 sm:px-8 py-3 sm:py-4 rounded-lg text-base sm:text-lg font-semibold hover:bg-white/20 transition-colors text-center w-full sm:w-auto">
                        {{ $secondaryButtonText }}
                    </a>
            </div>
            </div>

            @if(isset($form))
                <div class="booking-form order-2 lg:order-2 max-w-[600px] flex justify-items-center">
                    {{ $form }}
                    
                    <div class="flex flex-row sm:flex-row gap-3 sm:gap-4 w-full items-center justify-items-center block sm:hidden">
                    <a href="{{ $primaryButtonUrl }}"
                       class="text-left bg-orange-600 px-6 sm:px-8 py-3 sm:py-4 rounded-lg text-base sm:text-lg font-semibold hover:bg-orange-700 transition-colors text-center w-[70vw] sm:w-auto">
                        {{ $primaryButtonText }}
                    </a>
                    <a href="{{ $secondaryButtonUrl }}"
                       class="bg-white/10 border border-white/20 px-6 sm:px-8 py-3 sm:py-4 rounded-lg text-base sm:text-lg font-semibold hover:bg-white/20 transition-colors text-center w-full sm:w-auto">
                        {{ $secondaryButtonText }}
                    </a>
                </div>
                </div>
                
                
            @endif
            
            
        </div>
    </div>
</section>
