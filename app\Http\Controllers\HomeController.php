<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class HomeController extends Controller
{
    /**
     * Show the landing page
     */
    public function index()
    {
        return view('welcome');
    }

    /**
     * Show the about us page
     */
    public function about()
    {
        return view('about');
    }

    /**
     * Show the public tracking page
     */
    public function tracking()
    {
        return view('tracking');
    }

    /**
     * Track a booking by ID
     */
    public function trackBooking(Request $request)
    {
        $request->validate([
            'booking_id' => 'required|string'
        ]);

        $booking = Booking::where('booking_id', $request->booking_id)->first();

        if (!$booking) {
            return back()->withErrors(['booking_id' => 'Booking not found.']);
        }

        return view('tracking-result', compact('booking'));
    }

    /**
     * Show dashboard based on user role
     */
    public function dashboard()
    {
        $user = Auth::user();

        if ($user->isAdmin()) {
            return redirect()->route('admin.dashboard');
        } else {
            return redirect()->route('customer.dashboard');
        }
    }
}
