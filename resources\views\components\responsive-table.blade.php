@props([
    'headers' => [],
    'rows' => [],
    'actions' => true,
    'searchable' => false,
    'pagination' => null,
    'emptyMessage' => 'No data available',
    'emptyIcon' => 'fas fa-inbox'
])

<div class="dashboard-table-container">
    @if($searchable)
        <div class="dashboard-search-container mb-4">
            <i class="dashboard-search-icon fas fa-search"></i>
            <input type="text" 
                   class="dashboard-search-input" 
                   placeholder="Search..." 
                   id="table-search"
                   onkeyup="filterTable()">
        </div>
    @endif

    @if(count($rows) > 0)
        <div class="overflow-x-auto">
            <table class="dashboard-table w-full" id="responsive-table">
                <thead class="bg-gray-50">
                    <tr>
                        @foreach($headers as $header)
                            <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {{ $header['label'] }}
                            </th>
                        @endforeach
                        @if($actions)
                            <th class="px-4 sm:px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        @endif
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach($rows as $row)
                        <tr class="hover:bg-gray-50 transition-colors">
                            @foreach($headers as $index => $header)
                                <td class="px-4 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-900" 
                                    data-label="{{ $header['label'] }}">
                                    @if(isset($header['type']) && $header['type'] === 'status')
                                        <span class="dashboard-status-badge {{ $row[$header['key']]['class'] ?? '' }}">
                                            {{ $row[$header['key']]['value'] ?? $row[$header['key']] }}
                                        </span>
                                    @elseif(isset($header['type']) && $header['type'] === 'currency')
                                        CF$ {{ number_format($row[$header['key']], 2) }}
                                    @elseif(isset($header['type']) && $header['type'] === 'date')
                                        {{ \Carbon\Carbon::parse($row[$header['key']])->format('M j, Y') }}
                                    @elseif(isset($header['type']) && $header['type'] === 'datetime')
                                        {{ \Carbon\Carbon::parse($row[$header['key']])->format('M j, Y g:i A') }}
                                    @elseif(isset($header['type']) && $header['type'] === 'truncate')
                                        <span title="{{ $row[$header['key']] }}">
                                            {{ Str::limit($row[$header['key']], $header['limit'] ?? 30) }}
                                        </span>
                                    @else
                                        {{ $row[$header['key']] }}
                                    @endif
                                </td>
                            @endforeach
                            @if($actions)
                                <td class="px-4 sm:px-6 py-4 whitespace-nowrap text-right text-sm font-medium" 
                                    data-label="Actions">
                                    <div class="dashboard-actions">
                                        {{ $slot }}
                                    </div>
                                </td>
                            @endif
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        @if($pagination)
            <div class="dashboard-pagination">
                {{ $pagination->links() }}
            </div>
        @endif
    @else
        <div class="dashboard-empty-state">
            <i class="{{ $emptyIcon }}"></i>
            <h3>No Data Found</h3>
            <p>{{ $emptyMessage }}</p>
        </div>
    @endif
</div>

@if($searchable)
<script>
function filterTable() {
    const input = document.getElementById('table-search');
    const filter = input.value.toLowerCase();
    const table = document.getElementById('responsive-table');
    const rows = table.getElementsByTagName('tr');

    for (let i = 1; i < rows.length; i++) {
        const row = rows[i];
        const cells = row.getElementsByTagName('td');
        let found = false;

        for (let j = 0; j < cells.length; j++) {
            const cell = cells[j];
            if (cell.textContent.toLowerCase().indexOf(filter) > -1) {
                found = true;
                break;
            }
        }

        row.style.display = found ? '' : 'none';
    }
}
</script>
@endif

<style>
/* Additional mobile-specific table styles */
@media (max-width: 768px) {
    .dashboard-table-container .dashboard-actions {
        flex-direction: row;
        gap: 0.5rem;
        justify-content: flex-start;
        flex-wrap: wrap;
    }
    
    .dashboard-table-container .dashboard-actions .dashboard-icon-btn {
        width: auto;
        min-width: 44px;
        padding: 0.5rem;
        font-size: 0.75rem;
    }
    
    .dashboard-table-container .dashboard-actions .dashboard-icon-btn.has-text {
        padding: 0.5rem 0.75rem;
    }
    
    /* Ensure table cards don't overflow */
    .dashboard-table tr {
        word-wrap: break-word;
        overflow-wrap: break-word;
    }
    
    .dashboard-table td {
        max-width: 100%;
        overflow: hidden;
    }
    
    /* Status badges in mobile cards */
    .dashboard-table .dashboard-status-badge {
        display: inline-block;
        margin-bottom: 0.25rem;
    }
}

/* Very small screens */
@media (max-width: 320px) {
    .dashboard-table td {
        padding-left: 35% !important;
    }
    
    .dashboard-table td:before {
        width: 30%;
        font-size: 0.75rem;
    }
}
</style>
