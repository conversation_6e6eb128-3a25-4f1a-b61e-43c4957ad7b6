<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Branch extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'address',
        'latitude',
        'longitude',
        'phone',
        'email',
        'status',
        'description',
        'operating_hours',
        'manager_name',
        'manager_phone',
    ];

    protected $casts = [
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'status' => 'boolean',
        'operating_hours' => 'array',
    ];

    /**
     * Scope to get only active branches
     */
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    /**
     * Scope to get only inactive branches
     */
    public function scopeInactive($query)
    {
        return $query->where('status', false);
    }

    /**
     * Get bookings that originated from this branch
     */
    public function bookings()
    {
        return $this->hasMany(Booking::class, 'origin_branch_id');
    }

    /**
     * Check if branch is active
     */
    public function isActive(): bool
    {
        return $this->status === true;
    }

    /**
     * Get formatted operating hours
     */
    public function getFormattedOperatingHoursAttribute(): string
    {
        if (!$this->operating_hours) {
            return 'Not specified';
        }

        $hours = $this->operating_hours;
        if (isset($hours['monday']) && isset($hours['friday'])) {
            return "Mon-Fri: {$hours['monday']} - {$hours['friday']}";
        }

        return 'Custom hours';
    }

    /**
     * Calculate distance to a given point
     */
    public function distanceTo(float $lat, float $lng): float
    {
        $earthRadius = 6371; // Earth's radius in kilometers

        $latDelta = deg2rad($lat - $this->latitude);
        $lngDelta = deg2rad($lng - $this->longitude);

        $a = sin($latDelta / 2) * sin($latDelta / 2) +
             cos(deg2rad($this->latitude)) * cos(deg2rad($lat)) *
             sin($lngDelta / 2) * sin($lngDelta / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return round($earthRadius * $c, 2);
    }

    /**
     * Find nearest branch to given coordinates
     */
    public static function findNearest(float $lat, float $lng)
    {
        return static::active()
            ->get()
            ->sortBy(function ($branch) use ($lat, $lng) {
                return $branch->distanceTo($lat, $lng);
            })
            ->first();
    }


}
