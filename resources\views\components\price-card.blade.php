@props(['icon', 'title', 'weight', 'price', 'popular' => false, 'bookingUrl' => '#'])

<div class="price-card text-center p-4 sm:p-6 lg:p-8 rounded-xl sm:rounded-2xl mx-auto min-w-[95%] {{ $popular ? 'bg-white text-gray-900 shadow-2xl relative sm:scale-105' : 'glass-card text-white shadow-lg' }}">
    @if($popular)
        <div class="absolute top-0 -translate-y-1/2 left-1/2 -translate-x-1/2 bg-orange-600 text-white font-bold text-xs px-3 py-1 rounded-full uppercase tracking-wider">Most Popular</div>
    @endif

    <i class="{{ $icon }} text-3xl sm:text-4xl lg:text-5xl mb-4 sm:mb-6 {{ $popular ? 'text-orange-600' : 'text-white opacity-70' }}"></i>
    <h3 class="text-xl sm:text-2xl font-bold mb-1">{{ $title }}</h3>
    <p class="{{ $popular ? 'text-gray-500' : 'opacity-70' }} text-xs sm:text-sm mb-4">{{ $weight }}</p>
    <p class="text-3xl sm:text-4xl lg:text-5xl font-extrabold mb-4 sm:mb-6 {{ $popular ? 'text-orange-600' : '' }}">{{ $price }}</p>

    <a href="{{ $bookingUrl }}"
       class="{{ $popular ? 'bg-orange-600 text-white font-bold py-3 px-6 rounded-lg hover:bg-orange-700' : 'bg-white/20 hover:bg-white/30 font-semibold py-3 px-6 rounded-lg' }} transition-colors w-full block text-sm sm:text-base">
        Book Now
    </a>
</div>
