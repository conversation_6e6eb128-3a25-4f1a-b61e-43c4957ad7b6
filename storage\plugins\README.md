# Plugin Directory Structure

This directory contains the plugin management system files:

- `active/` - Contains activated plugins
- `inactive/` - Contains installed but inactive plugins  
- `uploads/` - Temporary storage for uploaded plugin files
- `temp/` - Temporary extraction directory for plugin processing

## Plugin Structure

Each plugin should have the following structure:
```
plugin-name/
├── plugin.json          # Plugin manifest file
├── PluginServiceProvider.php  # Main plugin class
├── routes/              # Plugin routes (optional)
├── views/               # Plugin views (optional)
├── config/              # Plugin configuration (optional)
└── assets/              # Plugin assets (optional)
```

## Security

- Only upload plugins from trusted sources
- All uploaded files are validated before extraction
- Plugins run in a sandboxed environment
