<?php

namespace App\Providers;

use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;


class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
            \App\Listeners\SendWelcomeEmail::class,
        ],

        // Booking automation events
        \App\Events\BookingCreated::class => [
            \App\Listeners\HandleBookingCreated::class,
        ],

        \App\Events\BookingStatusChanged::class => [
            \App\Listeners\HandleBookingStatusChanged::class,
            \App\Listeners\AwardRoyaltyPoints::class,
        ],


    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
