@extends('layouts.app')

@section('title', 'Customer Dashboard - TTAJet')

@section('content')
<style>
    main {
        margin-top: 0px !important;
    }
    </style>
<div class="bg-white dashboard-container">

    <!-- Header -->
    <div class="bg-white shadow-sm pt-[12vh] sm:pt-[4vh]">
        <div class="container mx-auto px-4 sm:px-6 py-6 sm:py-8">
            <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center">
                <div class="mb-4 lg:mb-0">
                    <h1 class="text-2xl sm:text-3xl font-bold text-gray-900">Welcome back, {{ auth()->user()->name }}!</h1>
                    <p class="text-gray-600 mt-1 text-sm sm:text-base">Manage your deliveries and track your packages</p>
                </div>
                <div class="dashboard-header-actions">
                    <a href="{{ route('booking.create') }}"
                       class="dashboard-icon-btn primary has-text">
                        <i class="fas fa-plus"></i>New Booking
                    </a>
                    <a href="{{ route('booking.history') }}"
                       class="dashboard-icon-btn secondary has-text">
                        <i class="fas fa-history"></i>View History
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container mx-auto px-4 sm:px-6 py-6 sm:py-8 dashboard-content">

        <!-- Statistics Cards -->
        <div class="dashboard-grid four-col grid lg:grid-cols-4 gap-6 mb-6 sm:mb-8">
            <div class="dashboard-stat-card">
                <div class="flex items-center">
                    <div class="p-2 sm:p-3 bg-blue-100 rounded-full flex-shrink-0">
                        <i class="fas fa-box text-blue-600 text-lg sm:text-xl"></i>
                    </div>
                    <div class="ml-3 sm:ml-4 min-w-0">
                        <p class="text-gray-500 text-xs sm:text-sm">Total Bookings</p>
                        <p class="text-xl sm:text-2xl font-bold text-gray-900">{{ $stats['total_bookings'] }}</p>
                    </div>
                </div>
            </div>

            <div class="dashboard-stat-card">
                <div class="flex items-center">
                    <div class="p-2 sm:p-3 bg-yellow-100 rounded-full flex-shrink-0">
                        <i class="fas fa-clock text-yellow-600 text-lg sm:text-xl"></i>
                    </div>
                    <div class="ml-3 sm:ml-4 min-w-0">
                        <p class="text-gray-500 text-xs sm:text-sm">Pending</p>
                        <p class="text-xl sm:text-2xl font-bold text-gray-900">{{ $stats['pending_bookings'] }}</p>
                    </div>
                </div>
            </div>

            <div class="dashboard-stat-card">
                <div class="flex items-center">
                    <div class="p-2 sm:p-3 bg-purple-100 rounded-full flex-shrink-0">
                        <i class="fas fa-truck text-purple-600 text-lg sm:text-xl"></i>
                    </div>
                    <div class="ml-3 sm:ml-4 min-w-0">
                        <p class="text-gray-500 text-xs sm:text-sm">Active</p>
                        <p class="text-xl sm:text-2xl font-bold text-gray-900">{{ $stats['in_transit_bookings'] }}</p>
                    </div>
                </div>
            </div>

            <div class="dashboard-stat-card">
                <div class="flex items-center">
                    <div class="p-2 sm:p-3 bg-green-100 rounded-full flex-shrink-0">
                        <i class="fas fa-check-circle text-green-600 text-lg sm:text-xl"></i>
                    </div>
                    <div class="ml-3 sm:ml-4 min-w-0">
                        <p class="text-gray-500 text-xs sm:text-sm">Delivered</p>
                        <p class="text-xl sm:text-2xl font-bold text-gray-900">{{ $stats['delivered_bookings'] }}</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="dashboard-grid three-col grid lg:grid-cols-3 gap-6 sm:gap-8"> 

            <!-- Recent Bookings -->
            <div class="lg:col-span-2">
                <div class="dashboard-card">
                    <div class="pb-4 sm:pb-6 border-b border-gray-200 mb-4 sm:mb-6">
                        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
                            <h2 class="text-lg sm:text-xl font-bold text-gray-900">Recent Bookings</h2>
                            <a href="{{ route('booking.history') }}" class="text-orange-500 hover:text-orange-600 text-sm font-semibold">
                                View All
                            </a>
                        </div>
                    </div>

                    <div>
                        @if($recentBookings->count() > 0)
                            <div class="space-y-3 sm:space-y-4">
                                @foreach($recentBookings as $booking)
                                    <div class="flex flex-col sm:flex-row sm:items-center justify-between p-3 sm:p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors gap-3 sm:gap-4">
                                        <div class="flex-1 min-w-0">
                                            <div class="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3">
                                                <div class="flex-shrink-0">
                                                    <span class="dashboard-status-badge
                                                        @if($booking->status === 'pending') bg-yellow-100 text-yellow-800
                                                        @elseif($booking->status === 'assigned') bg-blue-100 text-blue-800
                                                        @elseif(in_array($booking->status, ['picked_up', 'in_transit'])) bg-purple-100 text-purple-800
                                                        @elseif($booking->status === 'delivered') bg-green-100 text-green-800
                                                        @else bg-gray-100 text-gray-800
                                                        @endif">
                                                        {{ $booking->formatted_status }}
                                                    </span>
                                                </div>
                                                <div class="min-w-0">
                                                    <p class="font-semibold text-gray-900 text-sm sm:text-base">{{ $booking->booking_id }}</p>
                                                    <p class="text-xs sm:text-sm text-gray-600 truncate">{{ Str::limit($booking->pickup_address, 30) }} → {{ Str::limit($booking->delivery_address, 30) }}</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="text-left sm:text-right flex-shrink-0">
                                            <p class="font-semibold text-gray-900 text-sm sm:text-base">CF$ {{ number_format($booking->estimated_cost, 2) }}</p>
                                            <p class="text-xs sm:text-sm text-gray-500">{{ $booking->created_at->format('M j, Y') }}</p>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="dashboard-empty-state">
                                <i class="fas fa-box-open"></i>
                                <h3>No bookings yet</h3>
                                <p>Start by creating your first delivery booking</p>
                                <a href="{{ route('booking.create') }}" class="dashboard-icon-btn primary has-text">
                                    <i class="fas fa-plus"></i>Create Booking
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            
            <!-- Sidebar -->
            <div class="space-y-6">
                
                <!-- Active Tracking -->
                @if($activeBooking)
                    <div class="bg-white rounded-xl shadow-sm">
                        <div class="p-6 border-b border-gray-200">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-bold text-gray-900">Active Delivery</h3>
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse mr-2"></div>
                                    <span class="text-xs text-gray-500">Live</span>
                                </div>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="space-y-3 mb-4">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Booking ID:</span>
                                    <span class="font-semibold">{{ $activeBooking->booking_id }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Status:</span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        @switch($activeBooking->status)
                                            @case('confirmed')
                                                bg-blue-100 text-blue-800
                                                @break
                                            @case('in_progress')
                                                bg-purple-100 text-purple-800
                                                @break
                                            @default
                                                bg-gray-100 text-gray-800
                                        @endswitch
                                    ">
                                        {{ $activeBooking->formatted_status }}
                                    </span>
                                </div>
                                @if($activeBooking->estimated_duration_minutes)
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">ETA:</span>
                                        <span class="font-semibold text-green-600">~{{ $activeBooking->estimated_duration_minutes }} min</span>
                                    </div>
                                @endif
                            </div>

                            <!-- Mini Live Map Preview -->
                            @if(in_array($activeBooking->status, ['in_progress']))
                                <div class="mb-4">
                                    <x-live-tracking-card :booking="$activeBooking" height="200px" :showDetails="false" />
                                </div>
                            @endif

                            <div class="space-y-2">
                                <a href="{{ route('booking.show', $activeBooking) }}"
                                   class="block w-full text-center brand-orange text-white py-2 rounded-lg hover:bg-orange-600 transition-colors">
                                    <i class="fas fa-map-marker-alt mr-2"></i>Full Tracking View
                                </a>
                            </div>
                        </div>
                    </div>
                @endif

        @if(\App\Models\Setting::where('key', 'royalty_program_status')->first()->value ?? false)
        <!-- Royalty Points Balance -->
        <div class="mb-6 sm:mb-8">
            <x-glass-card class="bg-gray-800 hover:bg-gray-900 transition-colors duration-300">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-200">Points Balance</h3>
                        <p class="text-4xl font-bold text-white mt-2">{{ number_format(Auth::user()->royalty_points ?? 0) }}</p>
                    </div>
                    <i class="fas fa-star text-yellow-400 text-5xl opacity-80"></i>
                </div>
            </x-glass-card>
        </div>
        @endif
                
                <!-- Quick Actions -->
                <div class="bg-white rounded-xl shadow-sm">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-bold text-gray-900">Quick Actions</h3>
                    </div>
                    <div class="p-6 space-y-3">
                        <a href="{{ route('booking.create') }}" 
                           class="flex items-center p-3 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors">
                            <i class="fas fa-plus text-orange-500 mr-3"></i>
                            New Booking
                        </a>
                        <a href="{{ route('tracking') }}" 
                           class="flex items-center p-3 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors">
                            <i class="fas fa-search text-orange-500 mr-3"></i>
                            Track Package
                        </a>
                        <a href="{{ route('profile.edit') }}" 
                           class="flex items-center p-3 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors">
                            <i class="fas fa-user text-orange-500 mr-3"></i>
                            Edit Profile
                        </a>
                    </div>
                </div>
                
                <!-- Notifications -->
                <div class="bg-white rounded-xl shadow-sm">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-bold text-gray-900">Notifications</h3>
                            @if($unreadNotifications->count() > 0)
                                <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                                    {{ $unreadNotifications->count() }}
                                </span>
                            @endif
                        </div>
                    </div>
                    <div class="p-6">
                        @if($unreadNotifications->count() > 0)
                            <div class="space-y-3">
                                @foreach($unreadNotifications as $notification)
                                    <div class="p-3 bg-gray-50 rounded-lg">
                                        <p class="font-semibold text-sm text-gray-900">{{ $notification->title }}</p>
                                        <p class="text-xs text-gray-600 mt-1">{{ $notification->message }}</p>
                                        <p class="text-xs text-gray-400 mt-1">{{ $notification->created_at->diffForHumans() }}</p>
                                    </div>
                                @endforeach
                            </div>
                            <a href="{{ route('customer.notifications') }}" 
                               class="block text-center text-orange-500 hover:text-orange-600 text-sm font-semibold mt-4">
                                View All Notifications
                            </a>
                        @else
                            <div class="text-center py-4">
                                <i class="fas fa-bell-slash text-gray-300 text-2xl mb-2"></i>
                                <p class="text-gray-500 text-sm">No new notifications</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animate statistics cards
    gsap.from(".bg-white", {
        duration: 0.6,
        y: 30,
        opacity: 0,
        stagger: 0.1,
        ease: 'power2.out'
    });
});
</script>
@endpush
