<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ClearQueue extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'queue:clear-all';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear all jobs from the queue';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $count = DB::table('jobs')->count();
            DB::table('jobs')->delete();
            
            $this->info("✅ Cleared {$count} jobs from the queue");
            
            // Also clear failed jobs if any
            $failedCount = DB::table('failed_jobs')->count();
            if ($failedCount > 0) {
                DB::table('failed_jobs')->delete();
                $this->info("✅ Cleared {$failedCount} failed jobs");
            }
            
            return 0;
        } catch (\Exception $e) {
            $this->error("❌ Failed to clear queue: " . $e->getMessage());
            return 1;
        }
    }
}
