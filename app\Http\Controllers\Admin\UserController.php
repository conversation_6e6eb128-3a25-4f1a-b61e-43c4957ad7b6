<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Booking;
use App\Models\RoyaltyPointTransaction;
use App\Services\EmailService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rules;

class UserController extends Controller
{
    /**
     * Display a listing of users
     */
    public function index(Request $request)
    {
        $query = User::query();
        
        // Filter by role
        if ($request->filled('role')) {
            $query->where('role', $request->role);
        }
        
        // Filter by status
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->where('is_active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }
        
        // Search by name or email
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone_number', 'like', "%{$search}%");
            });
        }
        
        $users = $query->orderBy('created_at', 'desc')->paginate(20);
        
        // Get user counts for filter tabs
        $userCounts = [
            'all' => User::count(),
            'customers' => User::where('role', 'customer')->count(),
            'admins' => User::where('role', 'admin')->count(),
            'active' => User::where('is_active', true)->count(),
            'inactive' => User::where('is_active', false)->count(),
        ];
        
        return view('admin.users.index', compact('users', 'userCounts'));
    }
    
    /**
     * Display the specified user
     */
    public function show(User $user)
    {
        // Get user statistics
        $stats = [];
        
        if ($user->isCustomer()) {
            $stats = [
                'total_bookings' => $user->bookings()->count(),
                'completed_bookings' => $user->bookings()->where('status', 'completed')->count(),
                'cancelled_bookings' => $user->bookings()->where('status', 'cancelled')->count(),
                'total_spent' => $user->bookings()
                    ->where('status', 'completed')
                    ->sum(DB::raw('COALESCE(final_cost, estimated_cost)')),
            ];
        }
        
        // Get recent bookings
        $recentBookings = collect();
        if ($user->isCustomer()) {
            $recentBookings = $user->bookings()
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get();
        }
        
        // Load recent royalty point transactions for audit
        $transactions = RoyaltyPointTransaction::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->limit(20)
            ->get();

        return view('admin.users.show', compact('user', 'stats', 'recentBookings', 'transactions'));
    }

    
    /**
     * Suspend/Unsuspend a user
     */
    public function suspend(Request $request, User $user)
    {
        $request->validate([
            'action' => 'required|in:suspend,unsuspend',
            'reason' => 'nullable|string|max:500'
        ]);
        
        if ($user->isAdmin()) {
            return back()->withErrors(['error' => 'Cannot suspend admin users.']);
        }
        
        $isActive = $request->action === 'unsuspend';
        
        $user->update(['is_active' => $isActive]);
        
        $message = $isActive ? 'User activated successfully!' : 'User suspended successfully!';
        
        return back()->with('success', $message);
    }
    
    /**
     * Create a new user
     */
    public function create()
    {
        return view('admin.users.create');
    }
    
    /**
     * Store a new user
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:users'],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'phone_number' => ['nullable', 'string', 'max:20'],
            'role' => ['required', 'in:customer,admin'],
        ]);
        
        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'phone_number' => $request->phone_number,
            'role' => $request->role,
            'is_approved' => true,
            'is_active' => true,
        ]);

        // Send welcome email for customers
        if ($user->role === 'customer') {
            try {
                EmailService::sendWelcomeEmail($user);
                Log::info("Welcome email sent to new customer created by admin: {$user->email}");
            } catch (\Exception $e) {
                Log::error("Failed to send welcome email to {$user->email}: " . $e->getMessage());
            }
        }

        return redirect()->route('admin.users.show', $user)
            ->with('success', 'User created successfully!');
    }
    
    /**
     * Show the form for editing the specified user
     */
    public function edit(User $user)
    {
        return view('admin.users.edit', compact('user'));
    }
    
    /**
     * Update the specified user
     */
    public function update(Request $request, User $user)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:users,email,' . $user->id],
            'phone_number' => ['nullable', 'string', 'max:20'],
            'role' => ['required', 'in:customer,admin'],
            'is_active' => ['boolean'],
            'is_approved' => ['boolean'],
        ]);
        
        $user->update([
            'name' => $request->name,
            'email' => $request->email,
            'phone_number' => $request->phone_number,
            'role' => $request->role,
            'is_active' => $request->boolean('is_active'),
            'is_approved' => $request->boolean('is_approved'),
        ]);
        
        return back()->with('success', 'User updated successfully!');
    }
    
    /**
     * Remove the specified user
     */
    public function destroy(User $user)
    {
        if ($user->isAdmin()) {
            return back()->withErrors(['error' => 'Cannot delete admin users.']);
        }
        
        // Check if user has active bookings
        $activeBookings = $user->bookings()->whereIn('status', ['pending', 'confirmed', 'in_progress'])->count();
        if ($activeBookings > 0) {
            return back()->withErrors(['error' => 'Cannot delete user with active bookings.']);
        }
        
        $user->delete();
        
        return redirect()->route('admin.users.index')
            ->with('success', 'User deleted successfully!');
    }

    /**
     * Adjust user's royalty points.
     */
    public function adjustPoints(Request $request, User $user)
    {
        $request->validate([
            'points' => 'required|integer',
            'notes' => 'required|string',
        ]);

        DB::transaction(function () use ($request, $user) {
            // Lock user row to prevent race conditions during concurrent adjustments
            $lockedUser = User::lockForUpdate()->find($user->id);

            $lockedUser->royalty_points += (int) $request->points;
            $lockedUser->save();

            RoyaltyPointTransaction::create([
                'user_id' => $lockedUser->id,
                'points' => (int) $request->points,
                'transaction_type' => RoyaltyPointTransaction::TYPE_ADMIN_ADJUSTMENT,
                'notes' => $request->notes,
            ]);
        });

        return redirect()->route('admin.users.show', $user)
            ->with('success', 'Royalty points adjusted successfully!');
    }
}
