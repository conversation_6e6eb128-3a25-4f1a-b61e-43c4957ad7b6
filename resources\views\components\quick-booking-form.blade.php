@props(['action' => '#', 'method' => 'GET'])

<x-glass-card>
    <h2 class="text-xl sm:text-2xl font-bold mb-4 sm:mb-6">Quick Booking</h2>
    <form id="quick-booking-form" class="space-y-1 sm:space-y-4 min-w-[70vw] max-w-[70vh] sm:max-w-[4oopx] sm:min-w-[300px]" action="{{ $action }}" method="{{ $method }}">
        @if($method !== 'GET')
            @csrf
        @endif

        <div class="relative">
            <i class="fas fa-map-marker-alt absolute left-3 sm:left-4 top-1/2 -translate-y-1/2 text-gray-300 text-sm"></i>
            <input type="text" name="pickup_location" placeholder="Enter pickup location"
                   class="w-full p-3 sm:p-3 pl-10 sm:pl-12 rounded-lg bg-white/10 border border-white/20 focus:outline-none focus:ring-2 focus:ring-orange-500 text-sm sm:text-base"
                   style="font-size: 16px;">
        </div>

        <div class="relative">
            <i class="fas fa-map-marker-alt absolute left-3 sm:left-4 top-1/2 -translate-y-1/2 text-gray-300 text-sm"></i>
            <input type="text" name="delivery_location" placeholder="Enter delivery location"
                   class="w-full p-3 sm:p-3 pl-10 sm:pl-12 rounded-lg bg-white/10 border border-white/20 focus:outline-none focus:ring-2 focus:ring-orange-500 text-sm sm:text-base"
                   style="font-size: 16px;">
        </div>

        <div class="relative">
            <i class="fas fa-box absolute left-3 sm:left-4 top-1/2 -translate-y-1/2 text-gray-300 text-sm"></i>
            <select name="package_type"
                    class="w-full p-3 sm:p-3 pl-10 sm:pl-12 pr-10 rounded-lg bg-white/10 border border-white/20 focus:outline-none focus:ring-2 focus:ring-orange-500 appearance-none text-sm sm:text-base"
                    style="font-size: 16px;">
                <option class="bg-gray-800">Select package type</option>
                <option class="bg-gray-800" value="document">Document</option>
                <option class="bg-gray-800" value="small_box">Small Box</option>
                <option class="bg-gray-800" value="medium_box">Medium Box</option>
                <option class="bg-gray-800" value="large_box">Large Box</option>
            </select>
            <i class="fas fa-chevron-down absolute right-3 sm:right-4 top-1/2 -translate-y-1/2 text-gray-300 text-sm"></i>
        </div>

        <button type="submit" class="w-full bg-orange-600 text-white font-bold py-3 sm:py-3 rounded-lg hover:bg-orange-700 transition-colors text-sm sm:text-base">
            Get Started
        </button>
    </form>
</x-glass-card>
