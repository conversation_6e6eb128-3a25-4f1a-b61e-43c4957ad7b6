<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

/**
 * A service class to interact with various Google Maps Platform APIs.
 *
 * This class provides methods for geocoding, distance calculation, directions,
 * place autocomplete, and finding nearby places of interest. It includes
 * robust error handling and fallback mechanisms.
 */
class GoogleMapsService
{
    /** @var string The main API key for Google Maps services. */
    protected $apiKey;

    /** @var string The API key specifically for Geocoding services. */
    protected $geocodingApiKey;

    /** @var string The API key specifically for Directions and Distance Matrix services. */
    protected $directionsApiKey;

    /**
     * GoogleMapsService constructor.
     *
     * Initializes the service by loading API keys from the configuration.
     * It allows for specific keys for different services, falling back to a general key if not provided.
     */
    public function __construct()
    {
        $this->apiKey = config('services.google_maps.api_key');
        $this->geocodingApiKey = config('services.google_maps.geocoding_api_key', $this->apiKey);
        $this->directionsApiKey = config('services.google_maps.directions_api_key', $this->apiKey);
    }

    /**
     * Get a precise current location using the Google Geolocation API.
     *
     * This method provides a more accurate location based on cell tower and Wi-Fi signals.
     * It requires the client (browser or mobile app) to collect and send this data.
     *
     * @param array $options An array containing data about the local network environment.
     * - 'wifiAccessPoints': An array of Wi-Fi access point objects.
     * - 'cellTowers': An array of cell tower objects.
     * @return array An array with 'lat', 'lng', and 'accuracy' in meters.
     */
    public function geolocate(array $options = []): array
    {
        if (!$this->apiKey) {
            return ['error' => 'Google Maps API key not configured'];
        }

        try {
            // The request body must contain at least one of wifiAccessPoints or cellTowers.
            $payload = [];
            if (!empty($options['wifiAccessPoints'])) {
                $payload['wifiAccessPoints'] = $options['wifiAccessPoints'];
            }
            if (!empty($options['cellTowers'])) {
                $payload['cellTowers'] = $options['cellTowers'];
            }

            if (empty($payload)) {
                throw new \InvalidArgumentException('Geolocation requires wifiAccessPoints or cellTowers data.');
            }

            $response = Http::post('https://www.googleapis.com/geolocation/v1/geolocate?key=' . $this->apiKey, $payload);

            if ($response->failed()) {
                $errorData = $response->json();
                throw new \Exception('Geolocation API error: ' . ($errorData['error']['message'] ?? 'Unknown error'));
            }

            $data = $response->json();

            return [
                'lat' => $data['location']['lat'],
                'lng' => $data['location']['lng'],
                'accuracy' => $data['accuracy'],
            ];
        } catch (\Exception $e) {
            Log::error('Geolocation API failed: ' . $e->getMessage());
            return ['error' => 'Failed to determine current location.'];
        }
    }

    /**
     * Geocode an address to get latitude and longitude.
     *
     * @param string $address The address to geocode.
     * @return array An array with lat, lng, and formatted_address.
     * @throws \Exception If geocoding fails and no fallback is available.
     */
    public function geocodeAddress(string $address): array
    {
        if (!$this->geocodingApiKey) {
            Log::warning('Geocoding API key not configured. Falling back to mock coordinates.');
            return $this->getMockCoordinates($address);
        }

        try {
            $response = Http::get('https://maps.googleapis.com/maps/api/geocode/json', [
                'address' => $address,
                'key' => $this->geocodingApiKey,
                'region' => 'gh', // Bias results to Ghana
            ]);

            $data = $response->json();

            if ($data['status'] !== 'OK' || empty($data['results'])) {
                throw new \Exception('Unable to geocode address: ' . ($data['error_message'] ?? $data['status']));
            }

            $location = $data['results'][0]['geometry']['location'];

            return [
                'lat' => $location['lat'],
                'lng' => $location['lng'],
                'formatted_address' => $data['results'][0]['formatted_address'],
            ];
        } catch (\Exception $e) {
            Log::error('Geocoding failed for address "' . $address . '": ' . $e->getMessage());
            // Fallback to mock coordinates for development/testing
            return $this->getMockCoordinates($address);
        }
    }

    /**
     * Calculate distance between two points using Google Maps Distance Matrix API.
     *
     * @param float $originLat Latitude of the origin.
     * @param float $originLng Longitude of the origin.
     * @param float $destLat Latitude of the destination.
     * @param float $destLng Longitude of the destination.
     * @return float Distance in kilometers.
     */
    public function calculateDistance(float $originLat, float $originLng, float $destLat, float $destLng): float
    {
        if (!$this->directionsApiKey) {
            Log::warning('Directions API key not configured. Falling back to Haversine distance.');
            return $this->calculateHaversineDistance($originLat, $originLng, $destLat, $destLng);
        }

        try {
            $response = Http::get('https://maps.googleapis.com/maps/api/distancematrix/json', [
                'origins' => "{$originLat},{$originLng}",
                'destinations' => "{$destLat},{$destLng}",
                'units' => 'metric',
                'key' => $this->directionsApiKey,
            ]);

            $data = $response->json();

            if ($data['status'] !== 'OK' || empty($data['rows'][0]['elements'][0]['status'] === 'OK')) {
                throw new \Exception('Distance Matrix API error: ' . ($data['error_message'] ?? $data['status']));
            }

            $distanceMeters = $data['rows'][0]['elements'][0]['distance']['value'];
            return round($distanceMeters / 1000, 2); // Convert to kilometers

        } catch (\Exception $e) {
            Log::error('Distance calculation failed: ' . $e->getMessage());
            // Fallback to Haversine calculation on API failure
            return $this->calculateHaversineDistance($originLat, $originLng, $destLat, $destLng);
        }
    }

    /**
     * Calculate distance and duration between two points.
     *
     * @param float $originLat
     * @param float $originLng
     * @param float $destLat
     * @param float $destLng
     * @return array ['distance' => float, 'duration' => float] Distance in km, duration in minutes.
     */
    public function calculateDistanceAndDuration(float $originLat, float $originLng, float $destLat, float $destLng): array
    {
        if (!$this->directionsApiKey) {
            Log::warning('Directions API key not configured. Falling back to estimated duration.');
            return $this->getFallbackDistanceAndDuration($originLat, $originLng, $destLat, $destLng);
        }

        try {
            $response = Http::get('https://maps.googleapis.com/maps/api/distancematrix/json', [
                'origins' => "{$originLat},{$originLng}",
                'destinations' => "{$destLat},{$destLng}",
                'units' => 'metric',
                'key' => $this->directionsApiKey,
            ]);

            $data = $response->json();

            if ($data['status'] !== 'OK' || $data['rows'][0]['elements'][0]['status'] !== 'OK') {
                 throw new \Exception('Distance Matrix API error for duration: ' . ($data['error_message'] ?? $data['status']));
            }

            $element = $data['rows'][0]['elements'][0];
            $distanceMeters = $element['distance']['value'];
            $durationSeconds = $element['duration']['value'];

            return [
                'distance' => round($distanceMeters / 1000, 2), // Kilometers
                'duration' => round($durationSeconds / 60, 1)   // Minutes
            ];
        } catch (\Exception $e) {
            Log::error('Distance and duration calculation failed: ' . $e->getMessage());
            // Fallback to Haversine calculation and estimation
            return $this->getFallbackDistanceAndDuration($originLat, $originLng, $destLat, $destLng);
        }
    }

    /**
     * Get place predictions for autocomplete with enhanced location-aware suggestions.
     *
     * @param string $input The partial address or place name typed by the user.
     * @param array $options Optional parameters to refine the search.
     * - 'session_token': A unique token for the autocomplete session (recommended for billing).
     * - 'lat', 'lng': The user's current latitude and longitude to bias results.
     * - 'radius': The radius (in meters) around the lat/lng to search within. Defaults to 50000.
     * - 'types': The type of prediction to return (e.g., 'address', '(cities)', 'establishment'). Defaults to a broad search.
     * @return array A list of predictions or an error message.
     */
    public function getPlacePredictions(string $input, array $options = []): array
    {
        if (!$this->apiKey) {
            return ['error' => 'Google Maps API key not configured'];
        }

        try {
            $params = [
                'input' => $input,
                'key' => $this->apiKey,
                'components' => $options['components'] ?? 'country:gh', // Default restriction to Ghana
                'types' => $options['types'] ?? '(regions)',
            ];

            // Add session token for billing purposes (highly recommended)
            if (!empty($options['session_token'])) {
                $params['sessiontoken'] = $options['session_token'];
            }

            // Add location bias for more accurate, nearby results
            if (isset($options['lat']) && isset($options['lng'])) {
                $params['location'] = "{$options['lat']},{$options['lng']}";
                $params['radius'] = $options['radius'] ?? 50000; // 50km radius default
                $params['strictbounds'] = false; // Allow results outside the radius, but prefer inside
            }

            $response = Http::get('https://maps.googleapis.com/maps/api/place/autocomplete/json', $params);
            $data = $response->json();

            if ($data['status'] !== 'OK' && $data['status'] !== 'ZERO_RESULTS') {
                throw new \Exception('Places Autocomplete API error: ' . ($data['error_message'] ?? $data['status']));
            }

            return [
                'predictions' => $data['predictions'] ?? [],
                'status' => $data['status']
            ];
        } catch (\Exception $e) {
            Log::error('Places Autocomplete API failed: ' . $e->getMessage());
            return ['error' => 'Failed to get place predictions'];
        }
    }

    /**
     * Get detailed information for a specific place by its Place ID.
     *
     * @param string $placeId The unique identifier for the place.
     * @param string|null $sessionToken The session token from the autocomplete request.
     * @return array Detailed information about the place or an error message.
     */
    public function getPlaceDetails(string $placeId, ?string $sessionToken = null): array
    {
        if (!$this->apiKey) {
            return ['error' => 'Google Maps API key not configured'];
        }

        try {
            $params = [
                'place_id' => $placeId,
                // Request more fields useful for rendering landmarks
                'fields' => 'place_id,name,formatted_address,geometry,icon,types,business_status',
                'key' => $this->apiKey,
            ];
            
            if ($sessionToken) {
                $params['sessiontoken'] = $sessionToken;
            }

            $response = Http::get('https://maps.googleapis.com/maps/api/place/details/json', $params);
            $data = $response->json();

            if ($data['status'] !== 'OK') {
                throw new \Exception('Place Details API error: ' . ($data['error_message'] ?? $data['status']));
            }

            $result = $data['result'];
            $location = $result['geometry']['location'];

            return [
                'place_id' => $result['place_id'],
                'name' => $result['name'] ?? '',
                'formatted_address' => $result['formatted_address'],
                'lat' => $location['lat'],
                'lng' => $location['lng'],
                'icon' => $result['icon'] ?? null, // Icon URL for map marker
                'types' => $result['types'] ?? [], // e.g., ['restaurant', 'food', 'point_of_interest']
                'business_status' => $result['business_status'] ?? 'UNKNOWN', // e.g., 'OPERATIONAL'
            ];
        } catch (\Exception $e) {
            Log::error('Place Details API failed: ' . $e->getMessage());
            return ['error' => 'Failed to get place details'];
        }
    }

    /**
     * Find nearby places (landmarks, restaurants, etc.) based on a location.
     *
     * @param float $lat Latitude of the search center.
     * @param float $lng Longitude of the search center.
     * @param int $radius The radius in meters to search within. Max 50000.
     * @param string $type The type of place to search for (e.g., 'tourist_attraction', 'restaurant').
     * @return array A list of nearby places or an error message.
     */
    public function findNearbyPlaces(float $lat, float $lng, int $radius = 5000, string $type = ''): array
    {
        if (!$this->apiKey) {
            return ['error' => 'Google Maps API key not configured'];
        }

        try {
            $params = [
                'location' => "{$lat},{$lng}",
                'radius' => $radius,
                'key' => $this->apiKey,
            ];

            if (!empty($type)) {
                $params['type'] = $type;
            }

            $response = Http::get('https://maps.googleapis.com/maps/api/place/nearbysearch/json', $params);
            $data = $response->json();

            if ($data['status'] !== 'OK' && $data['status'] !== 'ZERO_RESULTS') {
                throw new \Exception('Nearby Search API error: ' . ($data['error_message'] ?? $data['status']));
            }

            return [
                'places' => $data['results'] ?? [],
                'status' => $data['status'],
            ];
        } catch (\Exception $e) {
            Log::error('Nearby Search API failed: ' . $e->getMessage());
            return ['error' => 'Failed to find nearby places'];
        }
    }

    // --- PROTECTED HELPER AND FALLBACK METHODS ---

    /**
     * Calculate "as the crow flies" distance using the Haversine formula.
     * This is a fallback for when the Distance Matrix API is unavailable.
     *
     * @param float $lat1
     * @param float $lng1
     * @param float $lat2
     * @param float $lng2
     * @return float Distance in kilometers.
     */
    protected function calculateHaversineDistance(float $lat1, float $lng1, float $lat2, float $lng2): float
    {
        $earthRadius = 6371; // Earth's radius in kilometers

        $latFrom = deg2rad($lat1);
        $lonFrom = deg2rad($lng1);
        $latTo = deg2rad($lat2);
        $lonTo = deg2rad($lng2);

        $latDelta = $latTo - $latFrom;
        $lonDelta = $lonTo - $lonFrom;

        $a = sin($latDelta / 2) ** 2 + cos($latFrom) * cos($latTo) * sin($lonDelta / 2) ** 2;
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return round($earthRadius * $c, 2);
    }
    
    /**
     * Provides a fallback calculation for distance and a rough estimate for duration.
     */
    private function getFallbackDistanceAndDuration(float $originLat, float $originLng, float $destLat, float $destLng): array
    {
        $distance = $this->calculateHaversineDistance($originLat, $originLng, $destLat, $destLng);
        // Rough estimate: 3 minutes per km, with a minimum of 15 minutes.
        $duration = max(15, round($distance * 3));

        return [
            'distance' => $distance,
            'duration' => $duration
        ];
    }

    /**
     * Get mock coordinates for testing/development (centered around Accra).
     */
    protected function getMockCoordinates(string $address): array
    {
        $accraLat = 5.6037;
        $accraLng = -0.1870;
        
        // Add some randomness within ~10km radius
        $latOffset = (rand(-100, 100) / 1000) * 0.1;
        $lngOffset = (rand(-100, 100) / 1000) * 0.1;

        return [
            'lat' => $accraLat + $latOffset,
            'lng' => $accraLng + $lngOffset,
            'formatted_address' => $address . ' (Mock Location), Accra, Ghana',
        ];
    }
    
    // Original methods like getDirections and reverseGeocode are preserved below for completeness.
    // They are unchanged from your original code.

    public function getDirections(float $originLat, float $originLng, float $destLat, float $destLng): array
    {
        if (!$this->directionsApiKey) {
            return ['error' => 'Google Maps API key not configured'];
        }
        try {
            $response = Http::get('https://maps.googleapis.com/maps/api/directions/json', [
                'origin' => $originLat . ',' . $originLng,
                'destination' => $destLat . ',' . $destLng,
                'key' => $this->directionsApiKey,
            ]);
            return $response->json();
        } catch (\Exception $e) {
            Log::error('Directions API failed: ' . $e->getMessage());
            return ['error' => 'Failed to get directions'];
        }
    }

    public function reverseGeocode(float $lat, float $lng): array
    {
        if (!$this->geocodingApiKey) {
            return ['error' => 'Google Maps API key not configured'];
        }
        try {
            $response = Http::get('https://maps.googleapis.com/maps/api/geocode/json', [
                'latlng' => $lat . ',' . $lng,
                'key' => $this->geocodingApiKey,
            ]);
            $data = $response->json();
            if ($data['status'] !== 'OK' || empty($data['results'])) {
                throw new \Exception('Unable to reverse geocode coordinates');
            }
            $result = $data['results'][0];
            return [
                'formatted_address' => $result['formatted_address'],
                'place_id' => $result['place_id'] ?? null,
                'lat' => $lat,
                'lng' => $lng,
            ];
        } catch (\Exception $e) {
            Log::error('Reverse geocoding failed: ' . $e->getMessage());
            return ['error' => 'Failed to reverse geocode coordinates'];
        }
    }
}
