<?php

namespace App\Services;

use App\Models\Booking;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

/**
 * Service for calculating and optimizing delivery routes using Google Maps.
 *
 * Provides advanced features including real-time traffic, multi-stop optimization,
 * landmark discovery, and dynamic ETA recalculation from a driver's live position.
 */
class RouteOptimizationService
{
    /** @var GoogleMapsService An instance of the GoogleMapsService for utility functions. */
    protected $googleMapsService;

    /**
     * RouteOptimizationService constructor.
     *
     * @param GoogleMapsService $googleMapsService Injected GoogleMapsService for dependency handling.
     */
    public function __construct(GoogleMapsService $googleMapsService)
    {
        $this->googleMapsService = $googleMapsService;
    }

    /**
     * Calculate an optimal route for a booking with advanced options.
     *
     * @param Booking $booking The booking to calculate the route for.
     * @param array $options Advanced routing options.
     * - 'waypoints': An array of coordinates (e.g., ['lat,lng', 'lat,lng']) for multi-stop routes.
     * - 'optimize_waypoints': (bool) If true, Google will determine the most efficient order for waypoints.
     * - 'avoid_tolls': (bool) Avoid toll roads.
     * - 'avoid_highways': (bool) Avoid highways.
     * - 'traffic_model': 'best_guess', 'pessimistic', or 'optimistic'.
     * @return array The calculated route data.
     */
    public function calculateRoute(Booking $booking, array $options = []): array
    {
        try {
            $cacheKey = $this->generateCacheKey($booking, $options);

            // Cache the result for 5 minutes (300 seconds)
            return Cache::remember($cacheKey, 300, function () use ($booking, $options) {
                // Increment a counter for API calls for stats tracking
                Cache::increment('google_maps_calls_today');
                return $this->fetchRouteFromGoogleMaps($booking, $options);
            });
        } catch (\Exception $e) {
            Log::error("Route calculation failed for booking {$booking->id}: " . $e->getMessage());
            return $this->getFallbackRoute($booking);
        }
    }

    /**
     * Recalculates the route and ETA from the driver's current position to the final destination.
     *
     * @param Booking $booking The active booking.
     * @param float $driverLat The driver's current latitude.
     * @param float $driverLng The driver's current longitude.
     * @param array $options The original routing options to maintain consistency.
     * @return array The updated route data from the driver's position.
     */
    public function recalculateRouteFromDriverPosition(Booking $booking, float $driverLat, float $driverLng, array $options = []): array
    {
        $options['origin_override'] = "{$driverLat},{$driverLng}";
        // We don't cache recalculated routes as they are highly dynamic
        try {
             return $this->fetchRouteFromGoogleMaps($booking, $options);
        } catch (\Exception $e) {
            Log::error("Route recalculation failed for booking {$booking->id}: " . $e->getMessage());
            // Fallback uses the new driver position for straight-line distance
            return $this->getFallbackRoute($booking, $driverLat, $driverLng);
        }
    }

    /**
     * Gets the route and also finds nearby landmarks and points of interest along the way.
     *
     * @param Booking $booking The booking for which to find the route and landmarks.
     * @param array $options Route options.
     * @param int $radius Radius in meters to search for landmarks around route points.
     * @param string $landmarkType The type of landmark to search for (e.g., 'tourist_attraction').
     * @return array Route data combined with a list of unique landmarks.
     */
    public function getRouteWithLandmarks(Booking $booking, array $options = [], int $radius = 2000, string $landmarkType = 'tourist_attraction'): array
    {
        $routeData = $this->calculateRoute($booking, $options);

        if (isset($routeData['fallback']) || empty($routeData['polyline'])) {
            $routeData['landmarks'] = [];
            return $routeData;
        }

        $routeData['landmarks'] = $this->findLandmarksAlongRoute($routeData['polyline'], $radius, $landmarkType);

        return $routeData;
    }

    /**
     * Fetches the route from the Google Maps Directions API.
     */
    protected function fetchRouteFromGoogleMaps(Booking $booking, array $options): array
    {
        $origin = $options['origin_override'] ?? "{$booking->pickup_latitude},{$booking->pickup_longitude}";
        $destination = "{$booking->delivery_latitude},{$booking->delivery_longitude}";

        $params = [
            'origin' => $origin,
            'destination' => $destination,
            'mode' => 'driving',
            'traffic_model' => $options['traffic_model'] ?? 'best_guess',
            'departure_time' => 'now',
            'key' => config('services.google.maps_api_key'),
        ];

        // Handle advanced options
        if (!empty($options['waypoints'])) {
            $waypoints = is_array($options['waypoints']) ? implode('|', $options['waypoints']) : $options['waypoints'];
            if (!empty($options['optimize_waypoints'])) {
                $waypoints = 'optimize:true|' . $waypoints;
            }
            $params['waypoints'] = $waypoints;
        }
        
        $avoids = [];
        if (!empty($options['avoid_tolls'])) $avoids[] = 'tolls';
        if (!empty($options['avoid_highways'])) $avoids[] = 'highways';
        if (!empty($avoids)) $params['avoid'] = implode('|', $avoids);


        $response = Http::get('https://maps.googleapis.com/maps/api/directions/json', $params);

        if ($response->successful() && $response->json('status') === 'OK') {
            return $this->parseGoogleMapsResponse($response->json());
        }

        throw new \Exception('Google Maps API request failed: ' . $response->json('error_message', $response->json('status', 'Unknown error')));
    }

    /**
     * Parses the detailed response from the Google Maps Directions API.
     */
    protected function parseGoogleMapsResponse(array $data): array
    {
        $route = $data['routes'][0];
        $legs = $route['legs'];

        $totalDistance = 0;
        $totalDuration = 0;
        $totalDurationInTraffic = 0;

        foreach ($legs as $leg) {
            $totalDistance += $leg['distance']['value'];
            $totalDuration += $leg['duration']['value'];
            $totalDurationInTraffic += $leg['duration_in_traffic']['value'] ?? $leg['duration']['value'];
        }

        $result = [
            'total_distance_km' => round($totalDistance / 1000, 2),
            'total_duration_minutes' => round($totalDuration / 60),
            'duration_in_traffic_minutes' => round($totalDurationInTraffic / 60),
            'eta' => now()->addMinutes(round($totalDurationInTraffic / 60)),
            'polyline' => $route['overview_polyline']['points'],
            'waypoint_order' => $route['waypoint_order'] ?? [], // For optimized routes
            'legs' => [],
        ];

        foreach ($legs as $leg) {
            $result['legs'][] = [
                'distance_km' => round($leg['distance']['value'] / 1000, 2),
                'duration_minutes' => round($leg['duration']['value'] / 60),
                'duration_in_traffic_minutes' => round(($leg['duration_in_traffic']['value'] ?? $leg['duration']['value']) / 60),
                'start_address' => $leg['start_address'],
                'end_address' => $leg['end_address'],
                'steps' => $this->parseSteps($leg['steps']),
            ];
        }

        return $result;
    }

    /**
     * Parses the turn-by-turn steps for a route leg.
     */
    protected function parseSteps(array $steps): array
    {
        return array_map(function ($step) {
            return [
                'distance_km' => round($step['distance']['value'] / 1000, 2),
                'duration_seconds' => $step['duration']['value'],
                'instruction' => strip_tags($step['html_instructions']),
                'maneuver' => $step['maneuver'] ?? 'none',
                'start_location' => $step['start_location'],
                'end_location' => $step['end_location'],
            ];
        }, $steps);
    }
    
    /**
     * Finds landmarks by sampling points along a route's polyline.
     */
    protected function findLandmarksAlongRoute(string $polyline, int $radius, string $type): array
    {
        // This is a simplified decode. For production, use a library like `geo-io/polyline-encoder`.
        $points = \Polyline::decode($polyline);
        $landmarks = [];
        $uniqueLandmarkIds = [];

        // Sample points every ~5km to avoid excessive API calls.
        $samplingDistanceKm = 5; 
        $lastSampledPoint = null;

        foreach ($points as $point) {
            if ($lastSampledPoint === null || $this->calculateStraightLineDistance($lastSampledPoint[0], $lastSampledPoint[1], $point[0], $point[1]) > $samplingDistanceKm) {
                
                $nearbyData = $this->googleMapsService->findNearbyPlaces($point[0], $point[1], $radius, $type);
                
                if (isset($nearbyData['places'])) {
                    foreach ($nearbyData['places'] as $place) {
                        if (!isset($uniqueLandmarkIds[$place['place_id']])) {
                            $landmarks[] = $place;
                            $uniqueLandmarkIds[$place['place_id']] = true;
                        }
                    }
                }
                $lastSampledPoint = $point;
            }
        }
        return $landmarks;
    }

    /**
     * Provides a fallback route calculation using straight-line distance.
     */
    protected function getFallbackRoute(Booking $booking, ?float $overrideLat = null, ?float $overrideLng = null): array
    {
        $startLat = $overrideLat ?? $booking->pickup_latitude;
        $startLng = $overrideLng ?? $booking->pickup_longitude;

        $distance = $this->calculateStraightLineDistance(
            $startLat,
            $startLng,
            $booking->delivery_latitude,
            $booking->delivery_longitude
        );

        // Estimate duration: 3 minutes per km + 20% for traffic, with a 15-minute minimum.
        $estimatedDuration = max(15, ($distance * 3) * 1.2);

        return [
            'total_distance_km' => round($distance, 2),
            'duration_in_traffic_minutes' => round($estimatedDuration),
            'eta' => now()->addMinutes(round($estimatedDuration)),
            'fallback' => true,
            'polyline' => '',
            'legs' => [],
        ];
    }

    /**
     * Calculates the straight-line (Haversine) distance between two points.
     */
    protected function calculateStraightLineDistance(float $lat1, float $lon1, float $lat2, float $lon2): float
    {
        $earthRadius = 6371; // km
        $dLat = deg2rad($lat2 - $lat1);
        $dLon = deg2rad($lon2 - $lon1);
        $a = sin($dLat / 2) * sin($dLat / 2) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * sin($dLon / 2) * sin($dLon / 2);
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
        return $earthRadius * $c;
    }

    /**
     * Generates a unique cache key based on the booking and route options.
     */
    protected function generateCacheKey(Booking $booking, array $options): string
    {
        // Sort options to ensure consistency in the key
        ksort($options);
        $optionsHash = md5(json_encode($options));
        return "route_booking_{$booking->id}_{$optionsHash}";
    }

    /**
     * Updates the ETA for all active bookings. Can be run as a scheduled job.
     */
    public function updateETAForActiveBookings(): void
    {
        $activeBookings = Booking::where('status', 'in_progress')->get();

        foreach ($activeBookings as $booking) {
            // Use default options for standard ETA updates
            $routeData = $this->calculateRoute($booking);

            if (!isset($routeData['fallback'])) {
                $booking->update([
                    'estimated_duration_minutes' => $routeData['duration_in_traffic_minutes'],
                    'distance_km' => $routeData['total_distance_km'],
                ]);
            }
        }
    }
}
