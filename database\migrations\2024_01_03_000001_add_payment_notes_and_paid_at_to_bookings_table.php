<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            // Add payment notes field for admin comments
            $table->text('payment_notes')->nullable()->after('customer_transaction_id');
            
            // Add paid_at timestamp for tracking when payment was marked as paid
            $table->timestamp('paid_at')->nullable()->after('payment_notes');
            
            // Update payment_status enum to include 'refunded' option
            $table->enum('payment_status', ['pending', 'paid', 'failed', 'refunded'])->default('pending')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            $table->dropColumn(['payment_notes', 'paid_at']);
            
            // Revert payment_status enum to original values
            $table->enum('payment_status', ['pending', 'paid', 'failed'])->default('pending')->change();
        });
    }
};
