{{-- Mobile Bottom Navigation Component --}}
<nav class="mobile-bottom-nav-modern">
    <div class="mobile-nav-pill">

        {{-- Home --}}
        <a href="{{ route('home') }}"
           class="mobile-nav-item {{ request()->routeIs('home') || request()->is('/') || request()->is('home') || request()->is('home/*') || (!request()->is('booking*') && !request()->is('tracking*') && !request()->is('login*') && !request()->is('register*') && !request()->is('customer*') && !request()->is('admin*') && request()->path() === '/') ? 'active' : '' }}">
            <i class="fas fa-house"></i>
            <span> HOME </span>
        </a>

        {{-- Book Delivery --}}
        <a href="{{ route('booking.create') }}"
           class="mobile-nav-item {{ request()->routeIs('booking.*') || request()->is('booking*') ? 'active' : '' }}">
            <i class="fa-solid fa-calendar-plus"></i>
            <span> BOOK </span>
        </a>

        {{-- Track --}}
        <a href="{{ route('tracking') }}"
           class="mobile-nav-item {{ request()->routeIs('tracking*') || request()->is('tracking*') || request()->is('track*') ? 'active' : '' }}">
            <i class="fa-solid fa-location-pin"></i>
            <span> TRACK </span>
        </a>

        {{-- Profile/Account --}}
        @guest
            <a href="{{ route('login') }}"
               class="mobile-nav-item {{ request()->routeIs('login') || request()->routeIs('register') || request()->is('login*') || request()->is('register*') ? 'active' : '' }}">
                <i class="fa-solid fa-circle-user"></i>
            <span> ACCOUNT </span>
            </a>
        @else
            @if(auth()->user()->isCustomer())
                <a href="{{ route('customer.dashboard') }}"
                   class="mobile-nav-item {{ request()->routeIs('customer.*') || request()->is('customer*') || request()->is('dashboard*') ? 'active' : '' }}">
                    <i class="fa-solid fa-circle-user"></i>
            <span> ACCOUNT </span>
                </a>
            @elseif(auth()->user()->isAdmin())
                <a href="{{ route('admin.dashboard') }}"
                   class="mobile-nav-item {{ request()->routeIs('admin.*') || request()->is('admin*') ? 'active' : '' }}">
                    <i class="fa-solid fa-bars-progress"></i>
                    <span> ADMIN </span>

                </a>
            @endif
        @endguest
    </div>
</nav>

{{-- Enhanced JavaScript for active and hover state management --}}
@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const navItems = document.querySelectorAll('.mobile-nav-item');

    // Define route patterns for each navigation item
    const routePatterns = {
        home: {
            paths: ['/', '/home'],
            routes: ['home'],
            contains: []
        },
        booking: {
            paths: ['/booking/create'],
            routes: ['booking.create', 'booking.edit', 'booking.show'],
            contains: ['/booking']
        },
        tracking: {
            paths: ['/tracking', '/track'],
            routes: ['tracking', 'tracking.show'],
            contains: ['/tracking', '/track']
        },
        login: {
            paths: ['/login', '/register'],
            routes: ['login', 'register'],
            contains: []
        },
        customer: {
            paths: ['/customer/dashboard'],
            routes: ['customer.dashboard', 'customer.profile', 'customer.orders'],
            contains: ['/customer', '/dashboard']
        },
        admin: {
            paths: ['/admin/dashboard'],
            routes: ['admin.dashboard', 'admin.users', 'admin.orders'],
            contains: ['/admin']
        }
    };

    // Function to determine which nav item should be active
    function getActiveNavType(currentPath, currentUrl) {
        const path = currentPath.toLowerCase();
        const fullUrl = currentUrl.toLowerCase();

        console.log('🏠 Checking path for active type:', { path, fullUrl });

        // Special handling for home page - check this first
        if (path === '' || path === '/' || path === '/home' ||
            path.endsWith('/') && path.slice(0, -1) === '' ||
            path === '/ttajetcom' || path.endsWith('/ttajetcom') ||
            fullUrl.includes('/home') && !fullUrl.includes('/booking') && !fullUrl.includes('/tracking') && !fullUrl.includes('/admin') && !fullUrl.includes('/customer')) {
            console.log('🏠 Detected HOME page');
            return 'home';
        }

        // Check contains patterns first (more specific)
        for (const [type, config] of Object.entries(routePatterns)) {
            if (config.contains.length > 0 && config.contains.some(pattern => path.includes(pattern))) {
                console.log(`📍 Detected ${type.toUpperCase()} via contains pattern`);
                return type;
            }
        }

        // Check exact path matches
        for (const [type, config] of Object.entries(routePatterns)) {
            if (config.paths.some(p => path === p || path === p + '/')) {
                console.log(`📍 Detected ${type.toUpperCase()} via exact path`);
                return type;
            }
        }

        console.log('❓ No specific type detected, defaulting to home');
        return 'home'; // Default to home if nothing else matches
    }

    // Function to update active states
    function updateActiveStates() {
        const currentPath = window.location.pathname;
        const currentUrl = window.location.href;

        console.log('🔍 Checking active states for:', currentPath);

        // Clear all active states first
        navItems.forEach(item => {
            item.classList.remove('active');
        });

        // Determine which type should be active
        const activeType = getActiveNavType(currentPath, currentUrl);
        console.log('📍 Active type determined:', activeType);

        if (!activeType) {
            console.log('❌ No active type found');
            return;
        }

        // Find and activate the corresponding nav item
        navItems.forEach(item => {
            const href = item.getAttribute('href');
            if (!href) return;

            let shouldActivate = false;
            const hrefLower = href.toLowerCase();

            // Check based on href patterns with improved home detection
            if (activeType === 'home') {
                // More flexible home detection
                if (hrefLower.includes('/home') || hrefLower === '/' ||
                    hrefLower.endsWith('/') && hrefLower.length <= 2 ||
                    hrefLower.includes('route(\'home\')') ||
                    item.querySelector('i.fa-house')) { // Also check by icon
                    shouldActivate = true;
                    console.log('🏠 Home nav item found:', href);
                }
            } else if (activeType === 'booking' && hrefLower.includes('/booking')) {
                shouldActivate = true;
            } else if (activeType === 'tracking' && hrefLower.includes('/tracking')) {
                shouldActivate = true;
            } else if (activeType === 'login' && hrefLower.includes('/login')) {
                shouldActivate = true;
            } else if (activeType === 'customer' && (hrefLower.includes('/customer') || hrefLower.includes('/dashboard'))) {
                shouldActivate = true;
            } else if (activeType === 'admin' && hrefLower.includes('/admin')) {
                shouldActivate = true;
            }

            if (shouldActivate) {
                item.classList.add('active');
                console.log('✅ Activated nav item:', href, 'for type:', activeType);
            }
        });
    }

    // Enhanced hover and interaction effects
    navItems.forEach(item => {
        // Mouse enter - add hover effect
        item.addEventListener('mouseenter', function() {
            if (!this.classList.contains('active')) {
                this.classList.add('hover');
            }
        });

        // Mouse leave - remove hover effect
        item.addEventListener('mouseleave', function() {
            this.classList.remove('hover');
        });

        // Touch start - add pressed effect for mobile
        item.addEventListener('touchstart', function() {
            this.classList.add('pressed');
        }, { passive: true });

        // Touch end - remove pressed effect
        item.addEventListener('touchend', function() {
            setTimeout(() => {
                this.classList.remove('pressed');
            }, 150);
        }, { passive: true });

        // Click handling with visual feedback
        item.addEventListener('click', function(e) {
            const href = this.getAttribute('href');

            // Add click animation
            this.classList.add('clicked');
            setTimeout(() => {
                this.classList.remove('clicked');
            }, 200);

            // Update active states after navigation
            setTimeout(updateActiveStates, 200);
        });
    });

    // Force initial update with multiple attempts
    setTimeout(updateActiveStates, 100);
    setTimeout(updateActiveStates, 500);
    setTimeout(updateActiveStates, 1000);

    // Update on various events
    window.addEventListener('load', updateActiveStates);
    window.addEventListener('hashchange', updateActiveStates);
    window.addEventListener('popstate', updateActiveStates);
    document.addEventListener('visibilitychange', function() {
        if (!document.hidden) {
            updateActiveStates();
        }
    });

    // Continuous monitoring for SPA-style navigation
    let lastPath = window.location.pathname;
    setInterval(function() {
        const currentPath = window.location.pathname;
        if (currentPath !== lastPath) {
            lastPath = currentPath;
            console.log('🔄 Path changed to:', currentPath);
            updateActiveStates();
        }
    }, 500);

    // Background-aware color adaptation system
    let lastBackgroundCheck = 0;
    let currentColorMode = 'dark'; // 'dark' or 'light'
    const SCROLL_THROTTLE = 100; // ms
    const COLOR_TRANSITION_DURATION = 300; // ms

    // Function to get average color from multiple sample points
    function sampleBackgroundColor() {
        const nav = document.querySelector('.mobile-bottom-nav-modern');
        if (!nav) return null;

        const navRect = nav.getBoundingClientRect();
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        // Set canvas size to match nav area
        canvas.width = navRect.width;
        canvas.height = navRect.height;

        try {
            // Create sample points across the navigation area
            const samplePoints = [
                { x: navRect.left + navRect.width * 0.2, y: navRect.top + navRect.height * 0.5 },
                { x: navRect.left + navRect.width * 0.4, y: navRect.top + navRect.height * 0.5 },
                { x: navRect.left + navRect.width * 0.6, y: navRect.top + navRect.height * 0.5 },
                { x: navRect.left + navRect.width * 0.8, y: navRect.top + navRect.height * 0.5 },
                { x: navRect.left + navRect.width * 0.5, y: navRect.top + navRect.height * 0.3 },
                { x: navRect.left + navRect.width * 0.5, y: navRect.top + navRect.height * 0.7 }
            ];

            let totalR = 0, totalG = 0, totalB = 0;
            let validSamples = 0;

            // Sample colors from elements behind the navigation
            samplePoints.forEach(point => {
                const element = document.elementFromPoint(point.x, point.y);
                if (element && element !== nav && !nav.contains(element)) {
                    const computedStyle = window.getComputedStyle(element);
                    const bgColor = computedStyle.backgroundColor;
                    const bgImage = computedStyle.backgroundImage;

                    // Parse background color
                    if (bgColor && bgColor !== 'rgba(0, 0, 0, 0)' && bgColor !== 'transparent') {
                        const rgb = parseRGBColor(bgColor);
                        if (rgb) {
                            totalR += rgb.r;
                            totalG += rgb.g;
                            totalB += rgb.b;
                            validSamples++;
                        }
                    }
                    // Handle background images by checking parent elements
                    else if (bgImage && bgImage !== 'none') {
                        // For images, we'll use a heuristic based on the element's text color
                        const textColor = computedStyle.color;
                        const rgb = parseRGBColor(textColor);
                        if (rgb) {
                            // Invert text color to estimate background
                            totalR += 255 - rgb.r;
                            totalG += 255 - rgb.g;
                            totalB += 255 - rgb.b;
                            validSamples++;
                        }
                    }
                }
            });

            // Fallback: check body background
            if (validSamples === 0) {
                const bodyStyle = window.getComputedStyle(document.body);
                const bodyBg = bodyStyle.backgroundColor;
                const rgb = parseRGBColor(bodyBg);
                if (rgb) {
                    totalR = rgb.r;
                    totalG = rgb.g;
                    totalB = rgb.b;
                    validSamples = 1;
                }
            }

            if (validSamples > 0) {
                return {
                    r: Math.round(totalR / validSamples),
                    g: Math.round(totalG / validSamples),
                    b: Math.round(totalB / validSamples)
                };
            }

        } catch (error) {
            console.warn('Background color sampling failed:', error);
        }

        return null;
    }

    // Function to parse RGB color string
    function parseRGBColor(colorStr) {
        if (!colorStr) return null;

        // Handle rgb() and rgba() formats
        const rgbMatch = colorStr.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*[\d.]+)?\)/);
        if (rgbMatch) {
            return {
                r: parseInt(rgbMatch[1]),
                g: parseInt(rgbMatch[2]),
                b: parseInt(rgbMatch[3])
            };
        }

        // Handle hex colors
        const hexMatch = colorStr.match(/^#([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i);
        if (hexMatch) {
            return {
                r: parseInt(hexMatch[1], 16),
                g: parseInt(hexMatch[2], 16),
                b: parseInt(hexMatch[3], 16)
            };
        }

        return null;
    }

    // Function to calculate luminance and determine if background is light or dark
    function isLightBackground(rgb) {
        if (!rgb) return false;

        // Calculate relative luminance using WCAG formula
        const luminance = (0.299 * rgb.r + 0.587 * rgb.g + 0.114 * rgb.b) / 255;
        return luminance > 0.5; // Threshold for light vs dark
    }

    // Function to apply color mode to navigation icons
    function applyColorMode(mode) {
        if (currentColorMode === mode) return;

        currentColorMode = mode;
        const inactiveItems = document.querySelectorAll('.mobile-nav-item:not(.active):not(.hover)');

        inactiveItems.forEach(item => {
            const icon = item.querySelector('i');
            if (icon) {
                // Remove existing color mode classes
                icon.classList.remove('nav-light-mode', 'nav-dark-mode');

                // Add appropriate color mode class
                if (mode === 'light') {
                    icon.classList.add('nav-light-mode');
                } else {
                    icon.classList.add('nav-dark-mode');
                }
            }
        });

        console.log(`🎨 Navigation color mode changed to: ${mode}`);
    }

    // Throttled scroll handler for background color detection
    function handleBackgroundColorChange() {
        const now = Date.now();
        if (now - lastBackgroundCheck < SCROLL_THROTTLE) return;

        lastBackgroundCheck = now;

        const backgroundColor = sampleBackgroundColor();
        if (backgroundColor) {
            const shouldUseLightMode = isLightBackground(backgroundColor);
            const newMode = shouldUseLightMode ? 'light' : 'dark';
            applyColorMode(newMode);
        }
    }

    // Optimized scroll event listener
    let scrollTimeout;
    function onScroll() {
        if (scrollTimeout) {
            clearTimeout(scrollTimeout);
        }

        scrollTimeout = setTimeout(handleBackgroundColorChange, 16); // ~60fps
    }

    // Resize handler to recalculate on layout changes
    function onResize() {
        setTimeout(handleBackgroundColorChange, 100);
    }

    // Initialize background color detection
    function initializeBackgroundDetection() {
        // Initial color detection
        setTimeout(handleBackgroundColorChange, 500);

        // Add event listeners
        window.addEventListener('scroll', onScroll, { passive: true });
        window.addEventListener('resize', onResize, { passive: true });

        // Also check when navigation state changes
        const originalUpdateActiveStates = updateActiveStates;
        updateActiveStates = function() {
            originalUpdateActiveStates();
            setTimeout(handleBackgroundColorChange, 100);
        };

        console.log('🎨 Background-aware color system initialized');
    }

    // Enhanced debug functions
    window.updateMobileNavActiveStates = updateActiveStates;
    window.debugMobileNav = function() {
        console.log('🔍 DEBUG INFO:');
        console.log('Current pathname:', window.location.pathname);
        console.log('Current href:', window.location.href);
        console.log('Nav items found:', navItems.length);
        console.log('Current color mode:', currentColorMode);

        navItems.forEach((item, index) => {
            const href = item.getAttribute('href');
            const hasActive = item.classList.contains('active');
            const icon = item.querySelector('i') ? item.querySelector('i').className : 'no icon';
            console.log(`Nav ${index + 1}: href="${href}", active=${hasActive}, icon="${icon}"`);
        });

        const activeType = getActiveNavType(window.location.pathname, window.location.href);
        console.log('Detected active type:', activeType);

        // Test background color detection
        const bgColor = sampleBackgroundColor();
        console.log('Sampled background color:', bgColor);
        if (bgColor) {
            console.log('Is light background:', isLightBackground(bgColor));
        }

        updateActiveStates();
    };

    window.testColorMode = function(mode) {
        console.log(`🧪 Testing color mode: ${mode}`);
        applyColorMode(mode);
    };

    // Initialize everything
    initializeBackgroundDetection();

    console.log('🚀 Mobile nav JavaScript loaded. Current path:', window.location.pathname);
    console.log('💡 Use window.debugMobileNav() to troubleshoot active states');
    console.log('🎨 Use window.testColorMode("light") or window.testColorMode("dark") to test color modes');
});
</script>
@endpush
