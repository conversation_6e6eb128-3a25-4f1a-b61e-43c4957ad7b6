<?php

use App\Http\Controllers\HomeController;
use App\Http\Controllers\BookingController;
use App\Http\Controllers\Admin\DashboardController as AdminDashboardController;
use App\Http\Controllers\Admin\BookingController as AdminBookingController;
use App\Http\Controllers\Admin\UserController as AdminUserController;
use App\Http\Controllers\Admin\SettingsController as AdminSettingsController;
use App\Http\Controllers\Customer\DashboardController as CustomerDashboardController;

use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
*/

// Public Routes
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/tracking', [HomeController::class, 'tracking'])->name('tracking');
Route::post('/track-booking', [HomeController::class, 'trackBooking'])->name('track.booking');
Route::get('/about', [HomeController::class, 'about'])->name('about');

// Authentication Routes
require __DIR__.'/auth.php';

// Protected Routes
Route::middleware('auth')->group(function () {
    
    // Dashboard Redirect
    Route::get('/dashboard', [HomeController::class, 'dashboard'])->name('dashboard');
    
    // Profile Routes
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
    
    // Booking Routes (All authenticated users can book)
    Route::prefix('booking')->name('booking.')->group(function () {
        Route::get('/create', [BookingController::class, 'create'])->name('create');
        Route::post('/store', [BookingController::class, 'store'])->name('store');
        Route::post('/calculate-cost', [BookingController::class, 'calculateCost'])->name('calculate-cost');
        Route::get('/{booking}', [BookingController::class, 'show'])->name('show');
        Route::get('/history/list', [BookingController::class, 'history'])->name('history');
    });
    
    // Customer Routes
    Route::middleware('role:customer')->prefix('customer')->name('customer.')->group(function () {
        Route::get('/dashboard', [CustomerDashboardController::class, 'index'])->name('dashboard');
        Route::get('/notifications', [CustomerDashboardController::class, 'notifications'])->name('notifications');
        Route::post('/notifications/{notification}/read', [CustomerDashboardController::class, 'markNotificationAsRead'])->name('notifications.read');
    });
    
    // Admin Routes
    Route::middleware('role:admin')->prefix('admin')->name('admin.')->group(function () {
        Route::get('/dashboard', [AdminDashboardController::class, 'index'])->name('dashboard');
        
        // Booking Management
        Route::prefix('bookings')->name('bookings.')->group(function () {
            Route::get('/', [AdminBookingController::class, 'index'])->name('index');
            Route::get('/{booking}', [AdminBookingController::class, 'show'])->name('show');
            Route::patch('/{booking}/status', [AdminBookingController::class, 'updateStatus'])->name('update-status');
            Route::patch('/{booking}/payment-status', [AdminBookingController::class, 'updatePaymentStatus'])->name('update-payment-status');
        });
        
        // User Management
        Route::prefix('users')->name('users.')->group(function () {
            Route::get('/', [AdminUserController::class, 'index'])->name('index');
            Route::get('/create', [AdminUserController::class, 'create'])->name('create');
            Route::post('/', [AdminUserController::class, 'store'])->name('store');
            Route::get('/{user}', [AdminUserController::class, 'show'])->name('show');
            Route::get('/{user}/edit', [AdminUserController::class, 'edit'])->name('edit');
            Route::patch('/{user}', [AdminUserController::class, 'update'])->name('update');

            Route::patch('/{user}/suspend', [AdminUserController::class, 'suspend'])->name('suspend');
            Route::delete('/{user}', [AdminUserController::class, 'destroy'])->name('destroy');
            Route::post('/{user}/points', [AdminUserController::class, 'adjustPoints'])->name('points.adjust');
        });
        
        // Settings
        Route::get('/settings', [AdminSettingsController::class, 'index'])->name('settings');
        Route::patch('/settings', [AdminSettingsController::class, 'update'])->name('settings.update');
        Route::post('/settings/test-email', [AdminSettingsController::class, 'testEmail'])->name('settings.test-email');
        Route::post('/settings/royalty', [AdminSettingsController::class, 'updateRoyaltySettings'])->name('settings.royalty.update');
    });
    

});



    // Live Map Routes
    Route::middleware('role:admin')->prefix('live-map')->name('live-map.')->group(function () {
        Route::get('/', [App\Http\Controllers\LiveMapController::class, 'index'])->name('index');
    });

    // Automation Management Routes
    Route::middleware('role:admin')->prefix('admin')->name('admin.')->group(function () {
        Route::get('/automation', function () {
            return view('admin.automation');
        })->name('automation');
    });

// API Routes for AJAX calls
Route::middleware('auth')->prefix('api')->name('api.')->group(function () {
    Route::post('/bookings/calculate-cost', [BookingController::class, 'calculateCost'])->name('bookings.calculate-cost');


    // Live Map API Routes
    Route::prefix('live-map')->name('live-map.')->group(function () {
        Route::get('/deliveries', [App\Http\Controllers\LiveMapController::class, 'getActiveDeliveries'])->name('deliveries');
        Route::get('/branches', [App\Http\Controllers\LiveMapController::class, 'getBranches'])->name('branches');
        Route::get('/booking/{booking}', [App\Http\Controllers\LiveMapController::class, 'getBookingTrackingData'])->name('booking');
    });
});
