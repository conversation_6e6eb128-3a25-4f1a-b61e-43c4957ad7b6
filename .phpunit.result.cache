{"version": 1, "defects": {"Tests\\Feature\\LoyaltyProgramTest::points_are_awarded_when_booking_is_confirmed": 8, "Tests\\Feature\\LoyaltyProgramTest::points_are_not_awarded_for_non_confirmed_status": 8, "Tests\\Feature\\LoyaltyProgramTest::user_can_pay_with_points_when_they_have_enough": 8, "Tests\\Feature\\LoyaltyProgramTest::points_payment_method_is_not_available_when_user_has_insufficient_points": 8, "Tests\\Feature\\LoyaltyProgramTest::loyalty_program_respects_disabled_status": 8}, "times": []}