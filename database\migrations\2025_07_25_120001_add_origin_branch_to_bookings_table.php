<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            $table->foreignId('origin_branch_id')->nullable()->after('user_id')->constrained('branches')->onDelete('set null');
            $table->index('origin_branch_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            $table->dropForeign(['origin_branch_id']);
            $table->dropIndex(['origin_branch_id']);
            $table->dropColumn('origin_branch_id');
        });
    }
};
