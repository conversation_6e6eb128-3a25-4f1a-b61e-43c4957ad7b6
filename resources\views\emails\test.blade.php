@extends('emails.layout')

@section('content')
<div class="info-card success">
    <h2>🎉 Email Configuration Test Successful!</h2>
    <p>Congratulations! Your TTAJet email system is properly configured and working correctly.</p>
</div>

<p>Dear Administrator,</p>

<p>This is a test email to confirm that your TTAJet Courier Service email system is functioning properly. If you're reading this message, it means:</p>

<ul style="margin: 20px 0; padding-left: 20px;">
    <li>✅ Your SMTP configuration is correct</li>
    <li>✅ Email templates are loading properly</li>
    <li>✅ The email service is operational</li>
    <li>✅ Emails can be sent successfully</li>
</ul>

<h3>Next Steps:</h3>
<p>Now that your email system is working, you can:</p>
<ul style="margin: 20px 0; padding-left: 20px;">
    <li>Configure admin email addresses in the settings</li>
    <li>Enable automatic email notifications for bookings</li>
    <li>Set up customer welcome emails</li>
    <li>Configure booking status update notifications</li>
</ul>

<div class="text-center" style="margin: 30px 0;">
    <a href="{{ config('app.url') }}/admin/settings" class="btn btn-primary">
        Configure Email Settings
    </a>
</div>

<div class="info-card">
    <h3>Email System Information:</h3>
    <table class="data-table">
        <tr>
            <td><strong>Test Date:</strong></td>
            <td>{{ now()->format('F j, Y \a\t g:i A') }}</td>
        </tr>
        <tr>
            <td><strong>System URL:</strong></td>
            <td>{{ config('app.url') }}</td>
        </tr>
        <tr>
            <td><strong>Email Driver:</strong></td>
            <td>{{ config('mail.default') }}</td>
        </tr>
        <tr>
            <td><strong>From Address:</strong></td>
            <td>{{ config('mail.from.address') }}</td>
        </tr>
    </table>
</div>

<p>If you have any questions or need assistance with the email system, please refer to the documentation or contact technical support.</p>

<p>Best regards,<br>
<strong>TTAJet Email System</strong></p>
@endsection
