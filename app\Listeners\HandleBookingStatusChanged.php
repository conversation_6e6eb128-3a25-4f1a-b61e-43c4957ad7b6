<?php

namespace App\Listeners;

use App\Events\BookingStatusChanged;
use App\Jobs\SendNotificationJob;
use App\Jobs\UpdateStatisticsJob;
// use App\Jobs\SyncDashboardDataJob; // TODO: File for this job appears to be missing.
use App\Services\EmailService;
use App\Services\RouteOptimizationService;
use Illuminate\Support\Facades\Log;

class HandleBookingStatusChanged
{

    protected $routeOptimizationService;

    /**
     * Create the event listener.
     */
    public function __construct(RouteOptimizationService $routeOptimizationService)
    {
        $this->routeOptimizationService = $routeOptimizationService;
    }

    /**
     * Handle the event.
     */
    public function handle(BookingStatusChanged $event): void
    {
        $booking = $event->booking;
        $previousStatus = $event->previousStatus ?? 'new';
        $newStatus = $event->newStatus;

        Log::info("Booking {$booking->booking_id} status changed from {$previousStatus} to {$newStatus}");

        try {
            // Handle specific status transitions
            $this->handleStatusTransition($booking, $previousStatus, $newStatus);

            // Send notifications based on status change
            $this->sendStatusChangeNotifications($booking, $previousStatus, $newStatus);

            // Update statistics
            $this->updateStatistics($booking, $newStatus);

            // Sync dashboard data
            $this->syncDashboards($booking);

        } catch (\Exception $e) {
            Log::error("Failed to handle status change for booking {$booking->booking_id}: " . $e->getMessage());
        }
    }

    /**
     * Handle specific status transitions
     */
    protected function handleStatusTransition($booking, ?string $previousStatus, string $newStatus): void
    {
        switch ($newStatus) {
            case 'in_progress':
                $this->handleInProgressTransition($booking);
                break;

            case 'delivered':
                $this->handleDeliveredTransition($booking);
                break;
                
            case 'cancelled':
                $this->handleCancelledTransition($booking, $previousStatus);
                break;
        }
    }

    /**
     * Handle transition to in_progress status
     */
    protected function handleInProgressTransition($booking): void
    {
        // Record pickup time
        $booking->update(['actual_pickup_time' => now()]);

        // Recalculate route for delivery
        $routeData = $this->routeOptimizationService->calculateRoute($booking);

        $booking->update([
            'estimated_duration_minutes' => $routeData['duration_in_traffic_minutes'] ?? $routeData['total_duration_minutes'],
            'distance_km' => $routeData['total_distance_km'],
        ]);

        Log::info("Booking {$booking->booking_id} marked as in progress with pickup time recorded");
    }

    /**
     * Handle transition to delivered status
     */
    protected function handleDeliveredTransition($booking): void
    {
        // Record delivery time
        $booking->update(['delivered_at' => now()]);

        // Calculate final cost if not already set
        if (!$booking->final_cost) {
            $booking->update(['final_cost' => $booking->estimated_cost]);
        }

        // Update payment status if cash payment
        if ($booking->payment_method === 'cash' && $booking->payment_status === 'pending') {
            $booking->update(['payment_status' => 'paid']);
        }


        Log::info("Booking {$booking->booking_id} completed with delivery time recorded");
    }

    /**
     * Handle transition to cancelled status
     */
    protected function handleCancelledTransition($booking, ?string $previousStatus): void
    {


        // Handle refunds if payment was processed
        if ($booking->payment_status === 'paid') {
            $this->initiateRefundProcess($booking);
        }

        Log::info("Booking {$booking->booking_id} cancelled from status {$previousStatus}");
    }

    /**
     * Send notifications based on status change
     */
    protected function sendStatusChangeNotifications($booking, ?string $previousStatus, string $newStatus): void
    {
        // Send email notification for status change
        try {
            // Get rider information if status is assigned
            $rider = null;
            if ($newStatus === 'assigned' && $booking->rider_id) {
                $rider = $booking->rider;
            }

            // Send status update email to customer
            EmailService::sendBookingStatusUpdate(
                $booking,
                $previousStatus,
                $rider
            );

            Log::info("Status change email sent for booking: {$booking->booking_id} ({$previousStatus} -> {$newStatus})");
        } catch (\Exception $e) {
            Log::error("Failed to send status change email for booking {$booking->booking_id}: " . $e->getMessage());
        }

        // Send in-app notifications
        $notifications = $this->getNotificationsForStatusChange($booking, $previousStatus, $newStatus);

        foreach ($notifications as $notification) {
            SendNotificationJob::dispatch($notification);
        }
    }

    /**
     * Get notifications for status change
     */
    protected function getNotificationsForStatusChange($booking, ?string $previousStatus, string $newStatus): array
    {
        $notifications = [];

        // Customer notifications
        $customerMessage = $this->getCustomerMessageForStatus($newStatus, $booking);
        if ($customerMessage) {
            $notifications[] = [
                'user_id' => $booking->user_id,
                'title' => 'Booking Update',
                'message' => $customerMessage,
                'type' => 'booking',
                'data' => [
                    'booking_id' => $booking->id,
                    'booking_reference' => $booking->booking_id,
                    'status' => $newStatus,
                    'previous_status' => $previousStatus,
                ],
            ];
        }


        return $notifications;
    }

    /**
     * Get customer message for status
     */
    protected function getCustomerMessageForStatus(string $status, $booking): ?string
    {
        return match ($status) {
            'confirmed' => "Your booking {$booking->booking_id} has been confirmed.",

            'in_progress' => "Your package is now being delivered for booking {$booking->booking_id}.",
            'delivered' => "Your package has been delivered successfully for booking {$booking->booking_id}.",
            'cancelled' => "Your booking {$booking->booking_id} has been cancelled.",
            default => null,
        };
    }


    /**
     * Update statistics
     */
    protected function updateStatistics($booking, string $newStatus): void
    {
        UpdateStatisticsJob::dispatch($booking, $newStatus);
    }

    /**
     * Sync dashboard data
     */
    protected function syncDashboards($booking): void
    {
        // SyncDashboardDataJob::dispatch($booking); // TODO: File for this job appears to be missing.
    }


    /**
     * Initiate refund process
     */
    protected function initiateRefundProcess($booking): void
    {
        // This would integrate with payment gateway for refunds
        Log::info("Refund process initiated for booking {$booking->booking_id}");
        
        // For now, just update payment status
        $booking->update(['payment_status' => 'refunded']);
    }

    /**
     * Handle a job failure.
     */
    public function failed(BookingStatusChanged $event, \Throwable $exception): void
    {
        Log::error("BookingStatusChanged listener failed for booking {$event->booking->booking_id}: " . $exception->getMessage());
    }
}
