<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>{{ $title ?? 'TTAJet Courier Service' }}</title>
    <style>
        /* Reset styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333333;
            background-color: #f9fafb;
        }

        /* Container */
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        /* Header */
        .email-header {
            background: linear-gradient(135deg, #F97316 0%, #ea580c 100%);
            padding: 30px 40px;
            text-align: center;
            color: white;
        }

        .logo {
            max-width: 150px;
            height: auto;
            margin-bottom: 15px;
        }

        .company-name {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 5px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .company-tagline {
            font-size: 14px;
            opacity: 0.9;
            font-weight: 400;
        }

        /* Title Section */
        .email-title {
            background-color: #ffffff;
            padding: 30px 40px 20px;
            border-bottom: 3px solid #F97316;
        }

        .email-title h1 {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 10px;
        }

        .email-subtitle {
            font-size: 16px;
            color: #6b7280;
            font-weight: 400;
        }

        /* Body Content */
        .email-body {
            padding: 30px 40px;
            background-color: #ffffff;
        }

        .email-body p {
            margin-bottom: 16px;
            font-size: 16px;
            line-height: 1.6;
            color: #374151;
        }

        .email-body h2 {
            font-size: 20px;
            font-weight: 600;
            color: #1f2937;
            margin: 25px 0 15px;
        }

        .email-body h3 {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin: 20px 0 10px;
        }

        /* Buttons */
        .btn {
            display: inline-block;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            transition: all 0.3s ease;
            margin: 10px 5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #F97316 0%, #ea580c 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(249, 115, 22, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(249, 115, 22, 0.4);
        }

        .btn-secondary {
            background-color: #f3f4f6;
            color: #374151;
            border: 2px solid #d1d5db;
        }

        .btn-secondary:hover {
            background-color: #e5e7eb;
            border-color: #9ca3af;
        }

        /* Info Cards */
        .info-card {
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .info-card.success {
            background-color: #f0fdf4;
            border-color: #bbf7d0;
        }

        .info-card.warning {
            background-color: #fffbeb;
            border-color: #fed7aa;
        }

        .info-card.error {
            background-color: #fef2f2;
            border-color: #fecaca;
        }

        /* Tables */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .data-table th,
        .data-table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }

        .data-table th {
            background-color: #f9fafb;
            font-weight: 600;
            color: #374151;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .data-table td {
            font-size: 15px;
            color: #4b5563;
        }

        .data-table tr:last-child td {
            border-bottom: none;
        }

        /* Footer */
        .email-footer {
            background-color: #1f2937;
            color: #d1d5db;
            padding: 30px 40px;
            text-align: center;
        }

        .footer-content {
            margin-bottom: 20px;
        }

        .footer-content h3 {
            color: #F97316;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .footer-content p {
            font-size: 14px;
            margin-bottom: 8px;
        }

        .footer-links {
            margin: 20px 0;
        }

        .footer-links a {
            color: #F97316;
            text-decoration: none;
            margin: 0 15px;
            font-size: 14px;
            font-weight: 500;
        }

        .footer-links a:hover {
            text-decoration: underline;
        }

        .footer-bottom {
            border-top: 1px solid #374151;
            padding-top: 20px;
            font-size: 12px;
            color: #9ca3af;
        }

        /* Responsive */
        @media only screen and (max-width: 600px) {
            .email-container {
                margin: 0;
                width: 100% !important;
            }

            .email-header,
            .email-title,
            .email-body,
            .email-footer {
                padding: 20px !important;
            }

            .company-name {
                font-size: 24px;
            }

            .email-title h1 {
                font-size: 20px;
            }

            .btn {
                display: block;
                margin: 10px 0;
                text-align: center;
            }

            .data-table {
                font-size: 14px;
            }

            .data-table th,
            .data-table td {
                padding: 8px 12px;
            }
        }

        /* Utility Classes */
        .text-center { text-align: center; }
        .text-left { text-align: left; }
        .text-right { text-align: right; }
        .mb-0 { margin-bottom: 0; }
        .mb-1 { margin-bottom: 8px; }
        .mb-2 { margin-bottom: 16px; }
        .mb-3 { margin-bottom: 24px; }
        .mt-0 { margin-top: 0; }
        .mt-1 { margin-top: 8px; }
        .mt-2 { margin-top: 16px; }
        .mt-3 { margin-top: 24px; }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header with Logo and Company Info -->
        <div class="email-header">
            @if(isset($showLogo) && $showLogo)
                <img src="{{ asset('images/logo.jpeg') }}" alt="TTAJet Logo" class="logo">
            @endif
            <div class="company-name">TTAJet Courier Service</div>
            <div class="company-tagline">Fast & Reliable Delivery Solutions</div>
        </div>

        <!-- Title Section -->
        @if(isset($title))
        <div class="email-title">
            <h1>{{ $title }}</h1>
            @if(isset($subtitle))
                <div class="email-subtitle">{{ $subtitle }}</div>
            @endif
        </div>
        @endif

        <!-- Main Content -->
        <div class="email-body">
            @yield('content')
        </div>

        <!-- Footer -->
        <div class="email-footer">
            <div class="footer-content">
                <h3>TTAJet Courier Service</h3>
                <p>{{ \App\Models\Setting::get('company.address', 'Ablekuma, Accra, Ghana') }}</p>
                <p>Phone: {{ \App\Models\Setting::get('company.phone', '+ 233 ************') }}</p>
                <p>Email: {{ \App\Models\Setting::get('company.email', '<EMAIL>') }}</p>
            </div>

            <div class="footer-links">
                <a href="{{ config('app.url') }}">Visit Website</a>
                <a href="{{ route('tracking') }}">Track Package</a>
                <a href="{{ route('booking.create') }}">Book Delivery</a>
            </div>

            <div class="footer-bottom">
                <p>&copy; {{ date('Y') }} TTAJet Courier Service. All rights reserved.</p>
                <p>This email was sent to {{ $recipientEmail ?? 'you' }} regarding your TTAJet account.</p>
            </div>
        </div>
    </div>
</body>
</html>
