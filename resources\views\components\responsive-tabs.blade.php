@props([
    'tabs' => [],
    'activeTab' => null,
    'tabsId' => 'responsive-tabs'
])

<div class="responsive-tabs-container" id="{{ $tabsId }}">
    <!-- Tab Navigation -->
    <div class="dashboard-tabs" role="tablist">
        @foreach($tabs as $index => $tab)
            <button class="dashboard-tab {{ ($activeTab === $tab['key'] || ($activeTab === null && $index === 0)) ? 'active' : '' }}"
                    role="tab"
                    aria-selected="{{ ($activeTab === $tab['key'] || ($activeTab === null && $index === 0)) ? 'true' : 'false' }}"
                    aria-controls="tab-panel-{{ $tab['key'] }}"
                    data-tab="{{ $tab['key'] }}"
                    onclick="switchTab('{{ $tabsId }}', '{{ $tab['key'] }}')">
                @if(isset($tab['icon']))
                    <i class="{{ $tab['icon'] }} mr-2"></i>
                @endif
                {{ $tab['label'] }}
                @if(isset($tab['count']))
                    <span class="ml-2 px-2 py-1 bg-gray-200 text-gray-700 rounded-full text-xs">
                        {{ $tab['count'] }}
                    </span>
                @endif
            </button>
        @endforeach
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
        @foreach($tabs as $index => $tab)
            <div class="tab-panel {{ ($activeTab === $tab['key'] || ($activeTab === null && $index === 0)) ? 'active' : '' }}"
                 id="tab-panel-{{ $tab['key'] }}"
                 role="tabpanel"
                 aria-labelledby="tab-{{ $tab['key'] }}">
                @if(isset($tab['content']))
                    {!! $tab['content'] !!}
                @else
                    {{ $slot }}
                @endif
            </div>
        @endforeach
    </div>
</div>

<script>
function switchTab(containerId, tabKey) {
    const container = document.getElementById(containerId);
    
    // Update tab buttons
    const tabs = container.querySelectorAll('.dashboard-tab');
    tabs.forEach(tab => {
        tab.classList.remove('active');
        tab.setAttribute('aria-selected', 'false');
    });
    
    const activeTab = container.querySelector(`[data-tab="${tabKey}"]`);
    if (activeTab) {
        activeTab.classList.add('active');
        activeTab.setAttribute('aria-selected', 'true');
    }
    
    // Update tab panels
    const panels = container.querySelectorAll('.tab-panel');
    panels.forEach(panel => {
        panel.classList.remove('active');
    });
    
    const activePanel = container.querySelector(`#tab-panel-${tabKey}`);
    if (activePanel) {
        activePanel.classList.add('active');
    }
    
    // Scroll tab into view on mobile
    if (window.innerWidth <= 768) {
        activeTab?.scrollIntoView({ 
            behavior: 'smooth', 
            block: 'nearest',
            inline: 'center'
        });
    }
}

// Touch swipe support for mobile
let startX = 0;
let startY = 0;

document.addEventListener('touchstart', function(e) {
    startX = e.touches[0].clientX;
    startY = e.touches[0].clientY;
});

document.addEventListener('touchend', function(e) {
    if (!startX || !startY) return;
    
    const endX = e.changedTouches[0].clientX;
    const endY = e.changedTouches[0].clientY;
    
    const diffX = startX - endX;
    const diffY = startY - endY;
    
    // Only handle horizontal swipes
    if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
        const tabContainer = e.target.closest('.responsive-tabs-container');
        if (!tabContainer) return;
        
        const activeTab = tabContainer.querySelector('.dashboard-tab.active');
        if (!activeTab) return;
        
        const tabs = Array.from(tabContainer.querySelectorAll('.dashboard-tab'));
        const currentIndex = tabs.indexOf(activeTab);
        
        let nextIndex;
        if (diffX > 0 && currentIndex < tabs.length - 1) {
            // Swipe left - next tab
            nextIndex = currentIndex + 1;
        } else if (diffX < 0 && currentIndex > 0) {
            // Swipe right - previous tab
            nextIndex = currentIndex - 1;
        }
        
        if (nextIndex !== undefined) {
            const nextTab = tabs[nextIndex];
            const tabKey = nextTab.getAttribute('data-tab');
            const containerId = tabContainer.id;
            switchTab(containerId, tabKey);
        }
    }
    
    startX = 0;
    startY = 0;
});
</script>

<style>
.responsive-tabs-container {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.tab-content {
    padding: 1.5rem;
}

.tab-panel {
    display: none;
}

.tab-panel.active {
    display: block;
}

/* Mobile-specific styles */
@media (max-width: 768px) {
    .responsive-tabs-container {
        border-radius: 0.5rem;
    }
    
    .tab-content {
        padding: 1rem;
    }
    
    /* Vertical tab layout for mobile */
    .dashboard-tabs {
        display: flex;
        flex-direction: row;
        overflow-x: auto;
        overflow-y: visible;
        max-height: none;
        border-bottom: 1px solid #e5e7eb;
        border-right: none;
        padding: 0.5rem;
        gap: 0.5rem;
    }
    
    .dashboard-tab {
        flex-shrink: 0;
        min-width: auto;
        padding: 0.75rem 1rem;
        border-radius: 0.5rem;
        border: 1px solid #e5e7eb;
        background: white;
        font-size: 0.875rem;
        white-space: nowrap;
    }
    
    .dashboard-tab.active {
        background-color: #f97316;
        color: white;
        border-color: #f97316;
    }
    
    .dashboard-tab:hover {
        background-color: #f9fafb;
        border-color: #d1d5db;
    }
    
    .dashboard-tab.active:hover {
        background-color: #ea580c;
        border-color: #ea580c;
    }
    
    /* Scroll indicators */
    .dashboard-tabs::before,
    .dashboard-tabs::after {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        width: 20px;
        pointer-events: none;
        z-index: 1;
    }
    
    .dashboard-tabs::before {
        left: 0;
        background: linear-gradient(to right, white, transparent);
    }
    
    .dashboard-tabs::after {
        right: 0;
        background: linear-gradient(to left, white, transparent);
    }
}

/* Very small screens */
@media (max-width: 320px) {
    .dashboard-tab {
        padding: 0.625rem 0.75rem;
        font-size: 0.8125rem;
    }
    
    .tab-content {
        padding: 0.75rem;
    }
}

/* Smooth transitions */
.dashboard-tab {
    transition: all 0.2s ease;
}

.tab-panel {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
