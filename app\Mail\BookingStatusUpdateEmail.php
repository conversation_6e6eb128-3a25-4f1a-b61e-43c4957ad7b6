<?php

namespace App\Mail;

use App\Models\Booking;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class BookingStatusUpdateEmail extends Mailable
{
    use Queueable, SerializesModels;

    public Booking $booking;
    public User $customer;
    public ?string $previousStatus;
    public ?User $rider;
    public ?string $cancellationReason;
    public ?string $failureReason;

    /**
     * Create a new message instance.
     */
    public function __construct(
        Booking $booking, 
        User $customer, 
        ?string $previousStatus = null,
        ?User $rider = null,
        ?string $cancellationReason = null,
        ?string $failureReason = null
    ) {
        $this->booking = $booking;
        $this->customer = $customer;
        $this->previousStatus = $previousStatus;
        $this->rider = $rider;
        $this->cancellationReason = $cancellationReason;
        $this->failureReason = $failureReason;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $statusText = ucfirst(str_replace('_', ' ', $this->booking->status));
        
        return new Envelope(
            subject: "Status Update: {$statusText} - {$this->booking->booking_id}",
            from: config('mail.from.address', '<EMAIL>'),
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.booking-status-update',
            with: [
                'booking' => $this->booking,
                'customer' => $this->customer,
                'previousStatus' => $this->previousStatus,
                'rider' => $this->rider,
                'cancellationReason' => $this->cancellationReason,
                'failureReason' => $this->failureReason,
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
