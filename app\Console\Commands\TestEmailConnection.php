<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use App\Services\EmailService;
use App\Models\User;

class TestEmailConnection extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:test-connection {email? : Email address to send test to}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test email connection and configuration';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔧 Testing Email Connection...');
        $this->newLine();

        // Display current configuration
        $this->displayConfiguration();
        $this->newLine();

        // Test email address
        $email = $this->argument('email') ?? $this->ask('Enter email address to send test to');
        
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $this->error('❌ Invalid email address provided.');
            return 1;
        }

        try {
            $this->info("📧 Sending test email to: {$email}");
            
            // Create a simple test user for the email
            $testUser = new User([
                'name' => 'Test User',
                'email' => $email,
                'role' => 'customer'
            ]);

            // Send test email using EmailService
            $result = EmailService::testEmailConfiguration($email);

            if (!$result) {
                throw new \Exception('Email sending failed - check logs for details');
            }
            
            $this->info('✅ Test email sent successfully!');
            $this->info('📬 Please check the recipient\'s inbox (and spam folder).');
            
        } catch (\Swift_TransportException $e) {
            $this->error('❌ SMTP Transport Error: ' . $e->getMessage());
            $this->newLine();
            $this->warn('💡 Common solutions:');
            $this->warn('   • Check MAIL_HOST, MAIL_PORT, MAIL_USERNAME, MAIL_PASSWORD');
            $this->warn('   • Verify SSL/TLS encryption settings');
            $this->warn('   • Ensure firewall allows outbound connections on port ' . config('mail.mailers.smtp.port'));
            $this->warn('   • Check if your email provider requires app-specific passwords');
            
        } catch (\Exception $e) {
            $this->error('❌ Email Error: ' . $e->getMessage());
            Log::error('Email test failed: ' . $e->getMessage());
            
            $this->newLine();
            $this->warn('💡 Check Laravel logs for more details:');
            $this->warn('   storage/logs/laravel.log');
        }

        return 0;
    }

    private function displayConfiguration()
    {
        $this->info('📋 Current Email Configuration:');
        $this->table(
            ['Setting', 'Value'],
            [
                ['MAIL_MAILER', config('mail.default')],
                ['MAIL_HOST', config('mail.mailers.smtp.host')],
                ['MAIL_PORT', config('mail.mailers.smtp.port')],
                ['MAIL_USERNAME', config('mail.mailers.smtp.username')],
                ['MAIL_PASSWORD', str_repeat('*', strlen(config('mail.mailers.smtp.password')))],
                ['MAIL_ENCRYPTION', config('mail.mailers.smtp.encryption')],
                ['MAIL_FROM_ADDRESS', config('mail.from.address')],
                ['MAIL_FROM_NAME', config('mail.from.name')],
            ]
        );
    }
}
