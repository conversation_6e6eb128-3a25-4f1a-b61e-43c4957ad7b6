@extends('layouts.app')

@section('title', 'User Details - Admin')

@section('content')
<div class="min-h-screen bg-gray-50">
    
    <!-- Header -->
    <div class="bg-white shadow-sm">
        <div class="container mx-auto px-6 py-8">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">User Details</h1>
                    <p class="text-gray-600 mt-1">{{ $user->name }} - {{ ucfirst($user->role) }}</p>
                </div>
                <div class="mt-4 md:mt-0 flex space-x-4">
                    <a href="{{ route('admin.users.edit', $user) }}" 
                       class="brand-orange text-white px-6 py-3 rounded-lg hover:bg-orange-600 transition-colors font-semibold">
                        <i class="fas fa-edit mr-2"></i>Edit User
                    </a>
                    <a href="{{ route('admin.users.index') }}" 
                       class="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors font-semibold">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Users
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container mx-auto px-6 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main User Details -->
            <div class="lg:col-span-2 space-y-6">
                <!-- User Information -->
                <div class="bg-white rounded-xl shadow-sm">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-bold text-gray-900">User Information</h3>
                    </div>
                    <div class="p-6">
                        <dl class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Full Name</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ $user->name }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Email Address</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ $user->email }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Phone Number</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ $user->phone_number ?? 'Not provided' }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Role</dt>
                                <dd class="mt-1">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        @switch($user->role)
                                            @case('admin')
                                                bg-red-100 text-red-800
                                                @break

                                            @case('customer')
                                                bg-green-100 text-green-800
                                                @break
                                            @default
                                                bg-gray-100 text-gray-800
                                        @endswitch
                                    ">
                                        {{ ucfirst($user->role) }}
                                    </span>
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Account Status</dt>
                                <dd class="mt-1">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $user->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                        {{ $user->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </dd>
                            </div>

                            <div>
                                <dt class="text-sm font-medium text-gray-500">Email Verified</dt>
                                <dd class="mt-1">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $user->email_verified_at ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                        {{ $user->email_verified_at ? 'Verified' : 'Not Verified' }}
                                    </span>
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Joined Date</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ $user->created_at->format('M d, Y \a\t g:i A') }}</dd>
                            </div>
                        </dl>
                    </div>
                </div>

                <!-- Statistics -->
                @if(!empty($stats))
                    <div class="bg-white rounded-xl shadow-sm">
                        <div class="p-6 border-b border-gray-200">
                            <h3 class="text-lg font-bold text-gray-900">Statistics</h3>
                        </div>
                        <div class="p-6">
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                @if($user->isCustomer())
                                    <div class="text-center">
                                        <div class="text-2xl font-bold text-gray-900">{{ $stats['total_bookings'] }}</div>
                                        <div class="text-sm text-gray-500">Total Bookings</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-2xl font-bold text-green-600">{{ $stats['completed_bookings'] }}</div>
                                        <div class="text-sm text-gray-500">Completed</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-2xl font-bold text-red-600">{{ $stats['cancelled_bookings'] }}</div>
                                        <div class="text-sm text-gray-500">Cancelled</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-2xl font-bold text-blue-600">{{ \App\Models\Setting::formatCurrency($stats['total_spent']) }}</div>
                                        <div class="text-sm text-gray-500">Total Spent</div>
                                    </div>

                                @endif
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Recent Activity -->
                @if($recentBookings->count() > 0)
                    <div class="bg-white rounded-xl shadow-sm">
                        <div class="p-6 border-b border-gray-200">
                            <h3 class="text-lg font-bold text-gray-900">Recent {{ $user->isCustomer() ? 'Bookings' : 'Jobs' }}</h3>
                        </div>
                        <div class="p-6">
                            <div class="space-y-4">
                                @foreach($recentBookings as $booking)
                                    <div class="border border-gray-200 rounded-lg p-4">
                                        <div class="flex justify-between items-start">
                                            <div>
                                                <h4 class="font-medium text-gray-900">{{ $booking->booking_id }}</h4>

                                                <p class="text-sm text-gray-500">{{ $booking->created_at->format('M d, Y g:i A') }}</p>
                                            </div>
                                            <div class="text-right">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                    @switch($booking->status)
                                                        @case('pending')
                                                            bg-yellow-100 text-yellow-800
                                                            @break
                                                        @case('confirmed')
                                                            bg-blue-100 text-blue-800
                                                            @break

                                                        @case('in_progress')
                                                            bg-purple-100 text-purple-800
                                                            @break
                                                        @case('completed')
                                                            bg-green-100 text-green-800
                                                            @break
                                                        @case('cancelled')
                                                            bg-red-100 text-red-800
                                                            @break
                                                        @default
                                                            bg-gray-100 text-gray-800
                                                    @endswitch
                                                ">
                                                    {{ ucfirst(str_replace('_', ' ', $booking->status)) }}
                                                </span>
                                                <div class="text-sm font-semibold text-gray-900 mt-1">
                                                    {{ \App\Models\Setting::formatCurrency($booking->final_cost ?? $booking->estimated_cost) }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Royalty Points Management -->
                <div class="bg-white rounded-xl shadow-sm">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-bold text-gray-900">Royalty Points Management</h3>
                    </div>
                    <div class="p-6">
                        <div class="mb-4">
                            <p class="text-sm text-gray-600">Current Balance</p>
                            <p class="text-2xl font-bold text-gray-900">{{ $user->royalty_points ?? 0 }} points</p>
                        </div>

                        <form action="{{ route('admin.users.points.adjust', $user) }}" method="POST">
                            @csrf
                            <div class="space-y-4">
                                <div>
                                    <label for="points" class="block text-sm font-medium text-gray-700">Points to Add/Subtract</label>
                                    <input type="number" name="points" id="points" required
                                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500 sm:text-sm">
                                </div>
                                <div>
                                    <label for="notes" class="block text-sm font-medium text-gray-700">Notes</label>
                                    <textarea name="notes" id="notes" rows="3" required
                                              class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500 sm:text-sm"></textarea>
                                </div>
                                <div>
                                    <button type="submit"
                                            class="w-full flex items-center justify-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors">
                                        Submit Adjustment
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Transaction History -->
                <div class="bg-white rounded-xl shadow-sm mt-6">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-bold text-gray-900">Recent Points Activity</h3>
                    </div>
                    <div class="p-6">
                        @if(isset($transactions) && $transactions->count())
                            <ul class="divide-y divide-gray-200">
                                @foreach($transactions as $tx)
                                    <li class="py-3 flex items-center justify-between">
                                        <div>
                                            <p class="text-sm text-gray-700">
                                                <span class="font-semibold">{{ ucfirst(str_replace('_', ' ', $tx->transaction_type)) }}</span>
                                                @if($tx->booking_id)
                                                    for booking <span class="font-mono">#{{ optional($tx->booking)->booking_id ?? $tx->booking_id }}</span>
                                                @endif
                                            </p>
                                            @if($tx->notes)
                                                <p class="text-xs text-gray-500">{{ $tx->notes }}</p>
                                            @endif
                                        </div>
                                        <div class="text-sm font-bold {{ $tx->points >= 0 ? 'text-green-600' : 'text-red-600' }}">
                                            {{ $tx->points >= 0 ? '+' : '' }}{{ $tx->points }} pts
                                        </div>
                                    </li>
                                @endforeach
                            </ul>
                        @else
                            <p class="text-sm text-gray-500">No recent transactions.</p>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Sidebar Actions -->
            <div class="space-y-6">
                <!-- Quick Actions -->
                <div class="bg-white rounded-xl shadow-sm">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-bold text-gray-900">Quick Actions</h3>
                    </div>
                    <div class="p-6 space-y-3">

                        
                        @if(!$user->isAdmin())
                            @if($user->is_active)
                                <form action="{{ route('admin.users.suspend', $user) }}" method="POST">
                                    @csrf
                                    @method('PATCH')
                                    <input type="hidden" name="action" value="suspend">
                                    <button type="submit" 
                                            class="w-full flex items-center justify-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                                            onclick="return confirm('Are you sure you want to suspend this user?')">
                                        <i class="fas fa-ban mr-2"></i>Suspend User
                                    </button>
                                </form>
                            @else
                                <form action="{{ route('admin.users.suspend', $user) }}" method="POST">
                                    @csrf
                                    @method('PATCH')
                                    <input type="hidden" name="action" value="unsuspend">
                                    <button type="submit" 
                                            class="w-full flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                                            onclick="return confirm('Are you sure you want to activate this user?')">
                                        <i class="fas fa-check mr-2"></i>Activate User
                                    </button>
                                </form>
                            @endif
                        @endif
                        
                        <a href="{{ route('admin.users.edit', $user) }}" 
                           class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                            <i class="fas fa-edit mr-2"></i>Edit Details
                        </a>
                        
                        @if(!$user->isAdmin())
                            <form action="{{ route('admin.users.destroy', $user) }}" method="POST">
                                @csrf
                                @method('DELETE')
                                <button type="submit" 
                                        class="w-full flex items-center justify-center px-4 py-2 border border-red-300 text-red-700 rounded-lg hover:bg-red-50 transition-colors"
                                        onclick="return confirm('Are you sure you want to delete this user? This action cannot be undone.')">
                                    <i class="fas fa-trash mr-2"></i>Delete User
                                </button>
                            </form>
                        @endif
                    </div>
                </div>

                <!-- Account Information -->
                <div class="bg-white rounded-xl shadow-sm">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-bold text-gray-900">Account Information</h3>
                    </div>
                    <div class="p-6 space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Last Login</span>
                            <span class="font-semibold">{{ $user->last_login_at ? $user->last_login_at->diffForHumans() : 'Never' }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Created</span>
                            <span class="font-semibold">{{ $user->created_at->diffForHumans() }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Updated</span>
                            <span class="font-semibold">{{ $user->updated_at->diffForHumans() }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
