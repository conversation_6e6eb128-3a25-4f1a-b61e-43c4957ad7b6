<?php

namespace App\Listeners;

// CHANGED: The listener now reacts to a "BookingCreated" event.
use App\Events\BookingCreated; 
use App\Models\Booking;
use App\Models\User;
use App\Models\Setting;
use App\Models\RoyaltyPointTransaction;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Throwable;

/**
 * Handles the awarding of royalty points to a customer as soon as they create a booking.
 *
 * This listener is queueable to offload work from the main request thread and uses
 * database transactions with pessimistic locking to ensure data integrity and prevent race conditions.
 */
class AwardRoyaltyPoints implements ShouldQueue
{
    /**
     * Define constants for setting keys to avoid "magic strings".
     */
    private const SETTING_ROYALTY_STATUS = 'royalty_program_status';
    private const SETTING_BOOKING_THRESHOLD = 'royalty_booking_threshold';
    private const SETTING_POINTS_PER_THRESHOLD = 'royalty_points_per_threshold';

    /**
     * The number of times the job may be attempted.
     * @var int
     */
    public $tries = 3;

    /**
     * Create the event listener.
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\BookingCreated  $event  // CHANGED: Event type is now BookingCreated
     * @return void
     */
    public function handle(BookingCreated $event)
    {
        // Guard clause: Ensure the booking is associated with a user.
        if (!$event->booking->user_id) {
            return;
        }

        try {
            // Use a database transaction to ensure all or nothing is committed.
            DB::transaction(function () use ($event) {
                $this->processRoyaltyPointsForBooking($event->booking);
            });
        } catch (Throwable $e) {
            Log::error('Failed to award royalty points on booking creation.', [
                'booking_id' => $event->booking->id,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Contains the core logic for processing and awarding royalty points.
     *
     * @param  \App\Models\Booking  $booking
     * @return void
     */
    private function processRoyaltyPointsForBooking(Booking $booking): void
    {
        // Check if the royalty program is active.
        $royaltyProgramStatus = Setting::where('key', self::SETTING_ROYALTY_STATUS)->first();
        if (!$royaltyProgramStatus || $royaltyProgramStatus->value !== '1') {
            return;
        }

        // Get the program rules.
        $bookingThresholdSetting = Setting::where('key', self::SETTING_BOOKING_THRESHOLD)->first();
        $pointsPerThresholdSetting = Setting::where('key', self::SETTING_POINTS_PER_THRESHOLD)->first();
        $bookingThreshold = $bookingThresholdSetting ? (int)$bookingThresholdSetting->value : 0;
        $pointsPerThreshold = $pointsPerThresholdSetting ? (int)$pointsPerThresholdSetting->value : 0;
        
        if ($bookingThreshold <= 0 || $pointsPerThreshold <= 0) {
            return;
        }

        // CRITICAL: Find and lock the user row to prevent race conditions.
        $customer = User::lockForUpdate()->find($booking->user_id);
        
        if (!$customer) {
            return; 
        }

        // The logic here remains the same: increment the counter, check, and reward.
        $customer->completed_bookings_for_reward += 1;

        if ($customer->completed_bookings_for_reward >= $bookingThreshold) {
            $customer->royalty_points += $pointsPerThreshold;
            $customer->completed_bookings_for_reward = 0; // Reset counter

            RoyaltyPointTransaction::create([
                'user_id' => $customer->id,
                'points' => $pointsPerThreshold,
                'transaction_type' => RoyaltyPointTransaction::TYPE_ACCRUAL,
                'notes' => "Awarded {$pointsPerThreshold} points for reaching {$bookingThreshold} bookings."
            ]);
        }

        $customer->save();
    }
}