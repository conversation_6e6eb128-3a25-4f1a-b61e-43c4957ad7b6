<?php

namespace Tests\Feature;

use App\Events\BookingStatusChanged;
use App\Models\Booking;
use App\Models\User;
use App\Models\Setting;
use App\Models\RoyaltyPointTransaction;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Tests\TestCase;

class LoyaltyProgramTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Enable loyalty program
        Setting::set('royalty_program_status', '1', 'boolean', 'royalty');
        Setting::set('royalty_booking_threshold', 2, 'number', 'royalty');
        Setting::set('royalty_points_per_threshold', 100, 'number', 'royalty');
        Setting::set('royalty_redemption_cost', 100, 'number', 'royalty');
    }

    /** @test */
    public function points_are_awarded_when_booking_is_confirmed()
    {
        $user = User::factory()->create([
            'role' => 'customer',
            'royalty_points' => 0,
            'completed_bookings_for_reward' => 1
        ]);

        $booking = Booking::factory()->create([
            'user_id' => $user->id,
            'status' => 'pending'
        ]);

        // Simulate booking confirmation
        event(new BookingStatusChanged($booking, 'pending', 'confirmed'));

        $user->refresh();
        
        // User should now have points and counter reset
        $this->assertEquals(100, $user->royalty_points);
        $this->assertEquals(0, $user->completed_bookings_for_reward);
        
        // Transaction should be recorded
        $this->assertDatabaseHas('royalty_point_transactions', [
            'user_id' => $user->id,
            'points' => 100,
            'transaction_type' => RoyaltyPointTransaction::TYPE_ACCRUAL
        ]);
    }

    /** @test */
    public function points_are_not_awarded_for_non_confirmed_status()
    {
        $user = User::factory()->create([
            'role' => 'customer',
            'royalty_points' => 0,
            'completed_bookings_for_reward' => 1
        ]);

        $booking = Booking::factory()->create([
            'user_id' => $user->id,
            'status' => 'pending'
        ]);

        // Simulate booking cancellation
        event(new BookingStatusChanged($booking, 'pending', 'cancelled'));

        $user->refresh();
        
        // User should not have received points
        $this->assertEquals(0, $user->royalty_points);
        $this->assertEquals(1, $user->completed_bookings_for_reward);
    }

    /** @test */
    public function user_can_pay_with_points_when_they_have_enough()
    {
        $user = User::factory()->create([
            'role' => 'customer',
            'royalty_points' => 150
        ]);

        $this->actingAs($user);

        $bookingData = [
            'pickup_address' => '123 Test St',
            'pickup_latitude' => 40.7128,
            'pickup_longitude' => -74.0060,
            'pickup_person_name' => 'John Doe',
            'pickup_person_phone' => '+1234567890',
            'delivery_address' => '456 Test Ave',
            'delivery_latitude' => 40.7589,
            'delivery_longitude' => -73.9851,
            'receiver_name' => 'Jane Doe',
            'receiver_phone' => '+0987654321',
            'package_type' => 'small',
            'package_weight' => 1.5,
            'pickup_time_preference' => 'now',
            'payment_method' => 'points',
        ];

        $response = $this->postJson('/booking', $bookingData);

        $response->assertJson(['success' => true]);
        
        $user->refresh();
        
        // Points should be deducted
        $this->assertEquals(50, $user->royalty_points);
        
        // Booking should be free
        $booking = Booking::where('user_id', $user->id)->first();
        $this->assertEquals(0, $booking->estimated_cost);
        $this->assertTrue($booking->used_royalty_points);
        
        // Redemption transaction should be recorded
        $this->assertDatabaseHas('royalty_point_transactions', [
            'user_id' => $user->id,
            'booking_id' => $booking->id,
            'points' => -100,
            'transaction_type' => RoyaltyPointTransaction::TYPE_REDEMPTION
        ]);
    }

    /** @test */
    public function points_payment_method_is_not_available_when_user_has_insufficient_points()
    {
        $user = User::factory()->create([
            'role' => 'customer',
            'royalty_points' => 50 // Less than redemption cost
        ]);

        $this->actingAs($user);

        $bookingData = [
            'pickup_address' => '123 Test St',
            'pickup_latitude' => 40.7128,
            'pickup_longitude' => -74.0060,
            'pickup_person_name' => 'John Doe',
            'pickup_person_phone' => '+1234567890',
            'delivery_address' => '456 Test Ave',
            'delivery_latitude' => 40.7589,
            'delivery_longitude' => -73.9851,
            'receiver_name' => 'Jane Doe',
            'receiver_phone' => '+0987654321',
            'package_type' => 'small',
            'package_weight' => 1.5,
            'pickup_time_preference' => 'now',
            'payment_method' => 'points',
        ];

        $response = $this->postJson('/booking', $bookingData);

        // Should fail validation because 'points' is not in valid payment methods
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['payment_method']);
    }

    /** @test */
    public function loyalty_program_respects_disabled_status()
    {
        // Disable loyalty program
        Setting::set('royalty_program_status', '0', 'boolean', 'royalty');

        $user = User::factory()->create([
            'role' => 'customer',
            'royalty_points' => 0,
            'completed_bookings_for_reward' => 1
        ]);

        $booking = Booking::factory()->create([
            'user_id' => $user->id,
            'status' => 'pending'
        ]);

        // Simulate booking confirmation
        event(new BookingStatusChanged($booking, 'pending', 'confirmed'));

        $user->refresh();
        
        // User should not have received points
        $this->assertEquals(0, $user->royalty_points);
        $this->assertEquals(1, $user->completed_bookings_for_reward);
    }
}
