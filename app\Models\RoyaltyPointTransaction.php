<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RoyaltyPointTransaction extends Model
{
    use HasFactory;

    /**
     * Transaction type constants
     */
    public const TYPE_ACCRUAL = 'accrual';
    public const TYPE_REDEMPTION = 'redemption';
    public const TYPE_ADMIN_ADJUSTMENT = 'admin_adjustment';

    protected $fillable = [
        'user_id',
        'booking_id',
        'points',
        'transaction_type',
        'notes',
    ];

    /**
     * Get the user that owns the transaction
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the booking associated with the transaction
     */
    public function booking()
    {
        return $this->belongsTo(Booking::class);
    }
}