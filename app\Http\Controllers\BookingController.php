<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\RoyaltyPointTransaction;
use App\Models\Setting;
use App\Services\CostCalculationService;
use App\Services\GoogleMapsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BookingController extends Controller
{
    protected $costCalculationService;
    protected $googleMapsService;

    public function __construct(CostCalculationService $costCalculationService, GoogleMapsService $googleMapsService)
    {
        $this->costCalculationService = $costCalculationService;
        $this->googleMapsService = $googleMapsService;
    }

    /**
     * Show the booking form
     */
    public function create()
    {
        $royaltySettings = Setting::getGroup('royalty');
        $can_redeem_points = false;
        $user = Auth::user();

        if (($royaltySettings['royalty_program_status'] ?? 'off') === 'on' && $user) {
            $redemptionCost = (int)($royaltySettings['royalty_redemption_cost'] ?? 0);
            if ($redemptionCost > 0 && $user->royalty_points >= $redemptionCost) {
                $can_redeem_points = true;
            }
        }

        return view('booking.create', compact('can_redeem_points'));
    }

    /**
     * Store a new booking
     */
    public function store(Request $request)
    {
        // Get available offline payment methods for validation
        $paymentSettings = \App\Models\Setting::getGroup('payments');
        $validPaymentMethods = ['cash']; // Always allow cash

        // Add offline payment methods that have names configured
        for ($i = 1; $i <= 3; $i++) {
            $methodName = $paymentSettings["offline_method_{$i}_name"] ?? '';
            if (!empty($methodName)) {
                $validPaymentMethods[] = "offline_method_{$i}";
            }
        }

        // Add points payment method if user has enough points
        $royaltySettings = Setting::getGroup('royalty');
        $user = Auth::user();
        if (($royaltySettings['royalty_program_status'] ?? 'off') === 'on' && $user) {
            $redemptionCost = (int)($royaltySettings['royalty_redemption_cost'] ?? 0);
            if ($redemptionCost > 0 && $user->royalty_points >= $redemptionCost) {
                $validPaymentMethods[] = 'points';
            }
        }

        $validationRules = [
            'pickup_address' => 'required|string|max:255',
            'delivery_address' => 'required|string|max:255',
            'pickup_latitude' => 'nullable|numeric|between:-90,90',
            'pickup_longitude' => 'nullable|numeric|between:-180,180',
            'pickup_place_id' => 'nullable|string|max:255',
            'delivery_latitude' => 'nullable|numeric|between:-90,90',
            'delivery_longitude' => 'nullable|numeric|between:-180,180',
            'delivery_place_id' => 'nullable|string|max:255',
            'pickup_person_name' => 'required|string|max:255',
            'pickup_person_phone' => 'required|string|max:20',
            'receiver_name' => 'required|string|max:255',
            'receiver_phone' => 'required|string|max:20',
            'package_type' => 'required|in:small,medium,large,document',
            'package_weight' => 'nullable|numeric|min:0|max:100',
            'pickup_time_preference' => 'required|in:now,1_hour,2_hours,scheduled',
            'payment_method' => 'required|in:' . implode(',', $validPaymentMethods),
            'special_instructions' => 'nullable|string|max:500',
        ];

        // Add conditional validation for offline payment feedback
        if ($request->filled('payment_method') && str_starts_with($request->payment_method, 'offline_method_')) {
            $selectedMethod = $request->payment_method;
            $validationRules["customer_payment_name_{$selectedMethod}"] = 'required|string|max:255';
            $validationRules["customer_transaction_id_{$selectedMethod}"] = 'required|string|max:255';
        }

        $request->validate($validationRules);

        try {
            DB::beginTransaction();

            // Get coordinates for addresses - use provided coordinates if available, otherwise geocode
            if ($request->pickup_latitude && $request->pickup_longitude) {
                $pickupCoords = [
                    'lat' => $request->pickup_latitude,
                    'lng' => $request->pickup_longitude
                ];
            } else {
                $pickupCoords = $this->googleMapsService->geocodeAddress($request->pickup_address);
            }

            if ($request->delivery_latitude && $request->delivery_longitude) {
                $deliveryCoords = [
                    'lat' => $request->delivery_latitude,
                    'lng' => $request->delivery_longitude
                ];
            } else {
                $deliveryCoords = $this->googleMapsService->geocodeAddress($request->delivery_address);
            }

            // Calculate distance and cost
            $distance = $this->googleMapsService->calculateDistance(
                $pickupCoords['lat'], $pickupCoords['lng'],
                $deliveryCoords['lat'], $deliveryCoords['lng']
            );

            $estimatedCost = $this->costCalculationService->calculateCost(
                $request->package_type,
                $request->package_weight ?? 0,
                $distance
            );

            // Prepare booking data
            $bookingData = [
                'user_id' => Auth::id(),
                'pickup_address' => $request->pickup_address,
                'pickup_latitude' => $pickupCoords['lat'],
                'pickup_longitude' => $pickupCoords['lng'],
                'pickup_place_id' => $request->pickup_place_id,
                'pickup_person_name' => $request->pickup_person_name,
                'pickup_person_phone' => $request->pickup_person_phone,
                'delivery_address' => $request->delivery_address,
                'delivery_latitude' => $deliveryCoords['lat'],
                'delivery_longitude' => $deliveryCoords['lng'],
                'delivery_place_id' => $request->delivery_place_id,
                'receiver_name' => $request->receiver_name,
                'receiver_phone' => $request->receiver_phone,
                'package_type' => $request->package_type,
                'package_weight' => $request->package_weight,
                'estimated_cost' => $estimatedCost,
                'payment_method' => $request->payment_method,
                'pickup_time_preference' => $request->pickup_time_preference,
                'distance_km' => $distance,
                'special_instructions' => $request->special_instructions,
            ];

            // Add offline payment feedback fields if provided (for the selected payment method)
            if (str_starts_with($request->payment_method, 'offline_method_')) {
                $selectedMethod = $request->payment_method;
                $paymentNameField = "customer_payment_name_{$selectedMethod}";
                $transactionIdField = "customer_transaction_id_{$selectedMethod}";

                if ($request->filled($paymentNameField)) {
                    $bookingData['customer_payment_name'] = $request->input($paymentNameField);
                }
                if ($request->filled($transactionIdField)) {
                    $bookingData['customer_transaction_id'] = $request->input($transactionIdField);
                }
            }

            // Create booking
            $booking = Booking::create($bookingData);

            // Handle royalty points redemption
            $shouldRedeemPoints = $request->payment_method === 'points';

            if ($shouldRedeemPoints) {
                $user = Auth::user();
                $royaltySettings = Setting::getGroup('royalty');
                $redemptionCost = (int)($royaltySettings['royalty_redemption_cost'] ?? 0);

                if (
                    ($royaltySettings['royalty_program_status'] ?? 'off') === 'on' &&
                    $redemptionCost > 0 &&
                    $user->royalty_points >= $redemptionCost
                ) {
                    // Update booking
                    $booking->estimated_cost = 0;
                    $booking->used_royalty_points = true;
                    $booking->save();

                    // Deduct points from user
                    $user->royalty_points -= $redemptionCost;
                    $user->save();

                    // Create transaction record
                    RoyaltyPointTransaction::create([
                        'user_id' => $user->id,
                        'booking_id' => $booking->id,
                        'points' => -$redemptionCost,
                        'transaction_type' => RoyaltyPointTransaction::TYPE_REDEMPTION,
                        'notes' => 'Redeemed for a free booking'
                    ]);
                }
            }


            DB::commit();

            return response()->json([
                'success' => true,
                'booking_id' => $booking->booking_id,
                'message' => 'Booking created successfully!'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Booking creation error', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to create booking. Please try again.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    /**
     * Calculate estimated cost for booking
     */
    public function calculateCost(Request $request)
    {
        $request->validate([
            'pickup_address' => 'required|string',
            'delivery_address' => 'required|string',
            'package_type' => 'required|in:small,medium,large,document',
            'package_weight' => 'nullable|numeric|min:0|max:100',
        ]);

        try {
            // Get coordinates for addresses
            $pickupCoords = $this->googleMapsService->geocodeAddress($request->pickup_address);
            $deliveryCoords = $this->googleMapsService->geocodeAddress($request->delivery_address);

            // Calculate distance and duration
            $routeData = $this->googleMapsService->calculateDistanceAndDuration(
                $pickupCoords['lat'], $pickupCoords['lng'],
                $deliveryCoords['lat'], $deliveryCoords['lng']
            );

            $distance = $routeData['distance'] ?? 0;
            $duration = $routeData['duration'] ?? 0;

            // Get cost breakdown
            $costBreakdown = $this->costCalculationService->getCostBreakdown(
                $request->package_type,
                $request->package_weight ?? 0,
                $distance
            );

            return response()->json([
                'success' => true,
                'estimated_cost' => $costBreakdown['total'],
                'distance_km' => $distance,
                'distance' => number_format($distance, 1) . ' km',
                'duration' => $this->formatDuration($duration),
                'formatted_cost' => \App\Models\Setting::formatCurrency($costBreakdown['total']),
                'breakdown' => [
                    'base_cost' => $costBreakdown['base_cost'],
                    'distance_cost' => $costBreakdown['distance_cost'],
                    'weight_cost' => $costBreakdown['weight_cost'],
                    'total' => $costBreakdown['total'],
                    'base_cost_formatted' => \App\Models\Setting::formatCurrency($costBreakdown['base_cost']),
                    'distance_cost_formatted' => \App\Models\Setting::formatCurrency($costBreakdown['distance_cost']),
                    'weight_cost_formatted' => \App\Models\Setting::formatCurrency($costBreakdown['weight_cost']),
                    'total_formatted' => \App\Models\Setting::formatCurrency($costBreakdown['total']),
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Cost calculation error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Unable to calculate cost. Please check the addresses.',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 400);
        }
    }

    /**
     * Format duration from minutes to human readable format
     */
    private function formatDuration($minutes)
    {
        if ($minutes < 60) {
            return round($minutes) . ' mins';
        }

        $hours = floor($minutes / 60);
        $remainingMinutes = $minutes % 60;

        if ($remainingMinutes == 0) {
            return $hours . ' hr' . ($hours > 1 ? 's' : '');
        }

        return $hours . ' hr' . ($hours > 1 ? 's' : '') . ' ' . round($remainingMinutes) . ' mins';
    }

    /**
     * Show booking details
     */
    public function show(Booking $booking)
    {
        // Ensure user can only view their own bookings (unless admin)
        if (!Auth::user()->isAdmin() && $booking->user_id !== Auth::id()) {
            abort(403);
        }

        return view('booking.show', compact('booking'));
    }

    /**
     * Show user's booking history
     */
    public function history()
    {
        $bookings = Auth::user()->bookings()
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('booking.history', compact('bookings'));
    }

    /**
     * Get place predictions for autocomplete
     */
    public function getPlacePredictions(Request $request)
    {
        $request->validate([
            'input' => 'required|string|min:2',
            'location' => 'nullable|string',
            'radius' => 'nullable|integer|min:1000|max:50000'
        ]);

        $options = [];
        if ($request->has('location') && $request->has('radius')) {
            $options['location'] = $request->location;
            $options['radius'] = $request->radius;
        }

        $result = $this->googleMapsService->getPlacePredictions($request->input, $options);

        return response()->json($result);
    }

    /**
     * Get place details by place ID
     */
    public function getPlaceDetails(Request $request)
    {
        $request->validate([
            'place_id' => 'required|string'
        ]);

        $result = $this->googleMapsService->getPlaceDetails($request->place_id);

        return response()->json($result);
    }

    /**
     * Reverse geocode coordinates to address
     */
    public function reverseGeocode(Request $request)
    {
        $request->validate([
            'lat' => 'required|numeric|between:-90,90',
            'lng' => 'required|numeric|between:-180,180'
        ]);

        $result = $this->googleMapsService->reverseGeocode($request->lat, $request->lng);

        return response()->json($result);
    }
}
