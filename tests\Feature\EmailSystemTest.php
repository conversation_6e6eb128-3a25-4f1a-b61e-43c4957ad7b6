<?php

namespace Tests\Feature;

use App\Mail\BookingConfirmationEmail;
use App\Mail\BookingStatusUpdateEmail;
use App\Mail\AdminNewBookingEmail;
use App\Mail\WelcomeEmail;
use App\Mail\PasswordResetEmail;
use App\Models\Booking;
use App\Models\User;
use App\Services\EmailService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

class EmailSystemTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Mail::fake();
    }

    /** @test */
    public function it_can_send_welcome_email()
    {
        $user = User::factory()->create(['role' => 'customer']);

        $result = EmailService::sendWelcomeEmail($user);

        $this->assertTrue($result);
        Mail::assertSent(WelcomeEmail::class, function ($mail) use ($user) {
            return $mail->hasTo($user->email) && $mail->user->id === $user->id;
        });
    }

    /** @test */
    public function it_can_send_booking_confirmation_email()
    {
        $user = User::factory()->create(['role' => 'customer']);
        $booking = Booking::factory()->create(['user_id' => $user->id]);

        $result = EmailService::sendBookingConfirmation($booking);

        $this->assertTrue($result);
        Mail::assertSent(BookingConfirmationEmail::class, function ($mail) use ($booking) {
            return $mail->hasTo($booking->customer->email) && $mail->booking->id === $booking->id;
        });
    }

    /** @test */
    public function it_can_send_booking_status_update_email()
    {
        $user = User::factory()->create(['role' => 'customer']);
        $booking = Booking::factory()->create(['user_id' => $user->id]);

        $result = EmailService::sendBookingStatusUpdate($booking, 'pending');

        $this->assertTrue($result);
        Mail::assertSent(BookingStatusUpdateEmail::class, function ($mail) use ($booking) {
            return $mail->hasTo($booking->customer->email) && $mail->booking->id === $booking->id;
        });
    }

    /** @test */
    public function it_can_send_admin_new_booking_notification()
    {
        $user = User::factory()->create(['role' => 'customer']);
        $booking = Booking::factory()->create(['user_id' => $user->id]);

        $result = EmailService::sendAdminNewBookingNotification($booking);

        $this->assertTrue($result);
        Mail::assertSent(AdminNewBookingEmail::class, function ($mail) use ($booking) {
            return $mail->booking->id === $booking->id;
        });
    }

    /** @test */
    public function it_can_send_password_reset_email()
    {
        $user = User::factory()->create(['role' => 'customer']);
        $resetUrl = 'https://example.com/reset?token=test-token';

        $result = EmailService::sendPasswordResetEmail($user, $resetUrl);

        $this->assertTrue($result);
        Mail::assertSent(PasswordResetEmail::class, function ($mail) use ($user, $resetUrl) {
            return $mail->hasTo($user->email) && 
                   $mail->user->id === $user->id && 
                   $mail->resetUrl === $resetUrl;
        });
    }

    /** @test */
    public function welcome_email_is_sent_when_user_registers()
    {
        $userData = [
            'name' => 'Test Customer',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'phone_number' => '+1234567890',
            'role' => 'customer'
        ];

        $response = $this->post('/register', $userData);

        $response->assertRedirect('/dashboard');
        Mail::assertSent(WelcomeEmail::class, function ($mail) use ($userData) {
            return $mail->hasTo($userData['email']);
        });
    }

    /** @test */
    public function booking_emails_are_sent_when_booking_is_created()
    {
        $user = User::factory()->create(['role' => 'customer']);
        $this->actingAs($user);

        $bookingData = [
            'pickup_address' => '123 Test St, Test City',
            'pickup_latitude' => 40.7128,
            'pickup_longitude' => -74.0060,
            'pickup_person_name' => 'John Doe',
            'pickup_person_phone' => '+1234567890',
            'delivery_address' => '456 Test Ave, Test City',
            'delivery_latitude' => 40.7589,
            'delivery_longitude' => -73.9851,
            'receiver_name' => 'Jane Doe',
            'receiver_phone' => '+0987654321',
            'package_type' => 'small',
            'package_weight' => 1.5,
            'pickup_time_preference' => 'now',
            'payment_method' => 'cash',
        ];

        $response = $this->postJson('/booking', $bookingData);

        $response->assertJson(['success' => true]);
        
        // Check that booking confirmation email was sent
        Mail::assertSent(BookingConfirmationEmail::class);
        
        // Check that admin notification email was sent
        Mail::assertSent(AdminNewBookingEmail::class);
    }

    /** @test */
    public function email_templates_render_correctly()
    {
        $user = User::factory()->create(['role' => 'customer']);
        $booking = Booking::factory()->create(['user_id' => $user->id]);

        // Test welcome email template
        $welcomeEmail = new WelcomeEmail($user);
        $welcomeContent = $welcomeEmail->render();
        $this->assertStringContainsString($user->name, $welcomeContent);
        $this->assertStringContainsString('TTAJet', $welcomeContent);

        // Test booking confirmation email template
        $confirmationEmail = new BookingConfirmationEmail($booking);
        $confirmationContent = $confirmationEmail->render();
        $this->assertStringContainsString($booking->booking_id, $confirmationContent);
        $this->assertStringContainsString($booking->customer->name, $confirmationContent);

        // Test status update email template
        $statusEmail = new BookingStatusUpdateEmail($booking, 'pending');
        $statusContent = $statusEmail->render();
        $this->assertStringContainsString($booking->booking_id, $statusContent);

        // Test admin notification email template
        $adminEmail = new AdminNewBookingEmail($booking);
        $adminContent = $adminEmail->render();
        $this->assertStringContainsString($booking->booking_id, $adminContent);
        $this->assertStringContainsString($booking->customer->name, $adminContent);

        // Test password reset email template
        $resetUrl = 'https://example.com/reset?token=test-token';
        $resetEmail = new PasswordResetEmail($user, $resetUrl);
        $resetContent = $resetEmail->render();
        $this->assertStringContainsString($user->name, $resetContent);
        $this->assertStringContainsString($resetUrl, $resetContent);
    }

    /** @test */
    public function emails_respect_notification_settings()
    {
        // Test that emails are not sent when disabled in settings
        \App\Models\Setting::set('notifications.booking_confirmation_emails', false);
        \App\Models\Setting::set('notifications.admin_new_booking_emails', false);
        \App\Models\Setting::set('notifications.welcome_emails', false);

        $user = User::factory()->create(['role' => 'customer']);
        $booking = Booking::factory()->create(['user_id' => $user->id]);

        // These should return false when disabled
        $this->assertFalse(EmailService::sendWelcomeEmail($user));
        $this->assertFalse(EmailService::sendBookingConfirmation($booking));
        $this->assertFalse(EmailService::sendAdminNewBookingNotification($booking));

        // No emails should be sent
        Mail::assertNothingSent();
    }
}
