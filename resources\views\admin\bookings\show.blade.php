@extends('layouts.app')

@section('title', 'Booking Details - Admin')

@section('content')
<div class="min-h-screen bg-gray-50">
    
    <!-- Header -->
    <div class="bg-white shadow-sm">
        <div class="container mx-auto px-6 py-8">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Booking Details</h1>
                    <p class="text-gray-600 mt-1">{{ $booking->booking_id }}</p>
                </div>
                <div class="mt-4 md:mt-0 flex space-x-4">
                    <a href="{{ route('admin.bookings.index') }}" 
                       class="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors font-semibold">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Bookings
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container mx-auto px-6 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Booking Details -->
            <div class="lg:col-span-2 space-y-6">

                <!-- Route Map -->
                <div class="bg-white rounded-xl shadow-sm">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-lg font-bold text-gray-900">
                                    <i class="fas fa-map-marker-alt text-orange-600 mr-2"></i>
                                    @if(in_array($booking->status, ['confirmed', 'in_progress']))
                                        Live Tracking
                                    @else
                                        Route Map
                                    @endif
                                </h3>
                                <p class="text-sm text-gray-600 mt-1">
                                    @if(in_array($booking->status, ['confirmed', 'in_progress']))
                                        Real-time delivery monitoring
                                    @else
                                        Pickup and delivery route
                                    @endif
                                </p>
                            </div>
                            @if(in_array($booking->status, ['confirmed', 'in_progress']))
                                <div class="flex items-center space-x-4">
                                    <button onclick="refreshAdminTracking()"
                                            class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                        <i class="fas fa-sync-alt mr-1" id="admin-refresh-icon"></i>
                                        Refresh
                                    </button>
                                </div>
                            @endif
                        </div>
                    </div>
                    <div class="p-0">
                        <div id="booking-map" style="height: 450px; width: 100%;"></div>
                    </div>
                </div>

                <!-- Booking Information -->
                <div class="bg-white rounded-xl shadow-sm">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-bold text-gray-900">Booking Information</h3>
                    </div>
                    <div class="p-6">
                        <dl class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Booking ID</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ $booking->booking_id }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Status</dt>
                                <dd class="mt-1">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        @switch($booking->status)
                                            @case('pending')
                                                bg-yellow-100 text-yellow-800
                                                @break
                                            @case('confirmed')
                                                bg-blue-100 text-blue-800
                                                @break

                                            @case('in_progress')
                                                bg-purple-100 text-purple-800
                                                @break
                                            @case('completed')
                                                bg-green-100 text-green-800
                                                @break
                                            @case('cancelled')
                                                bg-red-100 text-red-800
                                                @break
                                            @default
                                                bg-gray-100 text-gray-800
                                        @endswitch
                                    ">
                                        {{ ucfirst(str_replace('_', ' ', $booking->status)) }}
                                    </span>
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Package Type</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ ucfirst($booking->package_type) }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Package Weight</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ $booking->package_weight ?? 'N/A' }} kg</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Distance</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ $booking->distance_km ?? 'N/A' }} km</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Estimated Cost</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ \App\Models\Setting::formatCurrency($booking->estimated_cost) }}</dd>
                            </div>
                            @if($booking->final_cost)
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Final Cost</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ \App\Models\Setting::formatCurrency($booking->final_cost) }}</dd>
                                </div>
                            @endif
                        </dl>
                        
                        @if($booking->package_description)
                            <div class="mt-6">
                                <dt class="text-sm font-medium text-gray-500">Package Description</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ $booking->package_description }}</dd>
                            </div>
                        @endif
                        
                        @if($booking->special_instructions)
                            <div class="mt-6">
                                <dt class="text-sm font-medium text-gray-500">Special Instructions</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ $booking->special_instructions }}</dd>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Pickup & Delivery Details -->
                <div class="bg-white rounded-xl shadow-sm">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-bold text-gray-900">Pickup & Delivery Details</h3>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Pickup Details -->
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3">Pickup Information</h4>
                                <dl class="space-y-2">
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Address</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $booking->pickup_address }}</dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Contact Person</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $booking->pickup_person_name }}</dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Phone</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $booking->pickup_person_phone }}</dd>
                                    </div>
                                    @if($booking->scheduled_pickup_time)
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Scheduled Time</dt>
                                            <dd class="mt-1 text-sm text-gray-900">{{ $booking->scheduled_pickup_time->format('M d, Y \a\t g:i A') }}</dd>
                                        </div>
                                    @endif
                                </dl>
                            </div>
                            
                            <!-- Delivery Details -->
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3">Delivery Information</h4>
                                <dl class="space-y-2">
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Address</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $booking->delivery_address }}</dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Receiver Name</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $booking->receiver_name }}</dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Phone</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $booking->receiver_phone }}</dd>
                                    </div>
                                    @if($booking->delivered_at)
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Delivered At</dt>
                                            <dd class="mt-1 text-sm text-gray-900">{{ $booking->delivered_at->format('M d, Y \a\t g:i A') }}</dd>
                                        </div>
                                    @endif
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Customer Information -->
                <div class="bg-white rounded-xl shadow-sm">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-bold text-gray-900">Customer</h3>
                    </div>
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-12 w-12">
                                <div class="h-12 w-12 rounded-full bg-indigo-500 flex items-center justify-center">
                                    <span class="text-lg font-medium text-white">
                                        {{ substr($booking->customer->name, 0, 1) }}
                                    </span>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-900">{{ $booking->customer->name }}</div>
                                <div class="text-sm text-gray-500">{{ $booking->customer->email }}</div>
                                @if($booking->customer->phone_number)
                                    <div class="text-sm text-gray-500">{{ $booking->customer->phone_number }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                

                <!-- Payment Details -->
                <div class="bg-white rounded-xl shadow-sm">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-bold text-gray-900 flex items-center">
                            <i class="fas fa-credit-card text-green-600 mr-2"></i>
                            Payment Information
                        </h3>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Payment Method</dt>
                                <dd class="mt-1 text-sm text-gray-900">
                                    @if($booking->payment_method === 'cash')
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <i class="fas fa-money-bill-wave mr-1"></i>
                                            Cash on Delivery
                                        </span>
                                    @elseif(str_starts_with($booking->payment_method, 'offline_method_'))
                                        @php
                                            $paymentSettings = \App\Models\Setting::getGroup('payments');
                                            $methodName = $paymentSettings[$booking->payment_method . '_name'] ?? 'Offline Payment';
                                        @endphp
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            <i class="fas fa-university mr-1"></i>
                                            {{ $methodName }}
                                        </span>
                                    @else
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                            {{ ucfirst(str_replace('_', ' ', $booking->payment_method)) }}
                                        </span>
                                    @endif
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Payment Status</dt>
                                <dd class="mt-1">
                                    <div class="flex items-center space-x-3">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            @switch($booking->payment_status)
                                                @case('paid')
                                                    bg-green-100 text-green-800
                                                    @break
                                                @case('failed')
                                                    bg-red-100 text-red-800
                                                    @break
                                                @default
                                                    bg-yellow-100 text-yellow-800
                                            @endswitch
                                        " id="payment-status-badge">
                                            {{ ucfirst($booking->payment_status) }}
                                        </span>
                                        <button type="button"
                                                onclick="togglePaymentStatusSwitcher()"
                                                class="inline-flex items-center px-2 py-1 border border-gray-300 text-xs leading-4 font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500">
                                            <i class="fas fa-edit mr-1"></i>
                                            Change
                                        </button>
                                    </div>

                                    <!-- Payment Status Switcher (Hidden by default) -->
                                    <div id="payment-status-switcher" class="mt-3 hidden">
                                        <form action="{{ route('admin.bookings.update-payment-status', $booking) }}" method="POST" class="space-y-3">
                                            @csrf
                                            @method('PATCH')
                                            <div>
                                                <label for="payment_status" class="block text-xs font-medium text-gray-700 mb-1">Update Payment Status</label>
                                                <select name="payment_status" id="payment_status" required
                                                        class="block w-full text-sm border border-gray-300 rounded-md px-2 py-1 focus:ring-orange-500 focus:border-orange-500">
                                                    <option value="pending" {{ $booking->payment_status == 'pending' ? 'selected' : '' }}>Pending</option>
                                                    <option value="paid" {{ $booking->payment_status == 'paid' ? 'selected' : '' }}>Paid</option>
                                                    <option value="failed" {{ $booking->payment_status == 'failed' ? 'selected' : '' }}>Failed</option>
                                                    <option value="refunded" {{ $booking->payment_status == 'refunded' ? 'selected' : '' }}>Refunded</option>
                                                </select>
                                            </div>

                                            <div id="payment_notes_div" class="hidden">
                                                <label for="payment_notes" class="block text-xs font-medium text-gray-700 mb-1">Notes (Optional)</label>
                                                <textarea name="payment_notes" id="payment_notes" rows="2"
                                                          class="block w-full text-sm border border-gray-300 rounded-md px-2 py-1 focus:ring-orange-500 focus:border-orange-500"
                                                          placeholder="Add notes about payment status change..."></textarea>
                                            </div>

                                            <div class="flex space-x-2">
                                                <button type="submit"
                                                        class="inline-flex items-center px-3 py-1 border border-transparent text-xs leading-4 font-medium rounded text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500">
                                                    <i class="fas fa-check mr-1"></i>
                                                    Update
                                                </button>
                                                <button type="button"
                                                        onclick="togglePaymentStatusSwitcher()"
                                                        class="inline-flex items-center px-3 py-1 border border-gray-300 text-xs leading-4 font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500">
                                                    <i class="fas fa-times mr-1"></i>
                                                    Cancel
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </dd>
                            </div>
                        </div>

                        @if(str_starts_with($booking->payment_method, 'offline_method_') && ($booking->customer_payment_name || $booking->customer_transaction_id))
                            <div class="mt-6 pt-6 border-t border-gray-200">
                                <h4 class="text-sm font-medium text-gray-900 mb-4">Customer Payment Details</h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    @if($booking->customer_payment_name)
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Payment Name</dt>
                                            <dd class="mt-1 text-sm text-gray-900">{{ $booking->customer_payment_name }}</dd>
                                        </div>
                                    @endif
                                    @if($booking->customer_transaction_id)
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Transaction ID</dt>
                                            <dd class="mt-1 text-sm text-gray-900 font-mono">{{ $booking->customer_transaction_id }}</dd>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Status Update -->
                <div class="bg-white rounded-xl shadow-sm">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-bold text-gray-900">Update Status</h3>
                    </div>
                    <div class="p-6">
                        <form action="{{ route('admin.bookings.update-status', $booking) }}" method="POST">
                            @csrf
                            @method('PATCH')
                            <div class="space-y-4">
                                <div>
                                    <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                                    <select name="status" id="status" required 
                                            class="mt-1 block w-full border border-gray-300 rounded-lg px-3 py-2">
                                        <option value="pending" {{ $booking->status == 'pending' ? 'selected' : '' }}>Pending</option>
                                        <option value="confirmed" {{ $booking->status == 'confirmed' ? 'selected' : '' }}>Confirmed</option>

                                        <option value="in_progress" {{ $booking->status == 'in_progress' ? 'selected' : '' }}>In Progress</option>
                                        <option value="delivered" {{ $booking->status == 'delivered' ? 'selected' : '' }}>Delivered</option>
                                        <option value="cancelled" {{ $booking->status == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                    </select>
                                </div>
                                
                                <div id="cancellation_reason_div" style="display: none;">
                                    <label for="cancellation_reason" class="block text-sm font-medium text-gray-700">Cancellation Reason</label>
                                    <textarea name="cancellation_reason" id="cancellation_reason" rows="3" 
                                              class="mt-1 block w-full border border-gray-300 rounded-lg px-3 py-2"
                                              placeholder="Enter reason for cancellation..."></textarea>
                                </div>
                                
                                <button type="submit" 
                                        class="w-full brand-orange text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors">
                                    Update Status
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Google Maps API -->
<script async defer src="https://maps.googleapis.com/maps/api/js?key={{ config('services.google_maps.api_key') }}&libraries=places&callback=initGoogleMaps"></script>

<script>
    // Map variables
    let map;
    let directionsService;
    let directionsRenderer;
    let pickupMarker;
    let deliveryMarker;

    // Initialize Google Maps when API is loaded
    function initGoogleMaps() {
        console.log('Google Maps API loaded');
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeTrackingMap);
        } else {
            initializeTrackingMap();
        }
    }

    function initializeTrackingMap() {
        if (typeof google === 'undefined' || !google.maps) {
            console.warn('Google Maps API not loaded yet');
            return;
        }

        // Initialize map
        const mapElement = document.getElementById('booking-map');
        if (mapElement) {
            map = new google.maps.Map(mapElement, {
                zoom: 12,
                center: { lat: 5.6037, lng: -0.1870 }, // Accra, Ghana
                mapTypeId: google.maps.MapTypeId.ROADMAP,
                styles: [
                    {
                        featureType: 'poi',
                        elementType: 'labels',
                        stylers: [{ visibility: 'off' }]
                    }
                ]
            });

            directionsService = new google.maps.DirectionsService();
            directionsRenderer = new google.maps.DirectionsRenderer({
                suppressMarkers: true,
                polylineOptions: {
                    strokeColor: '#F59E0B',
                    strokeWeight: 4
                }
            });
            directionsRenderer.setMap(map);

            // Load booking route
            updateMapAndRoute();
        }

        // Handle tab switching for map resize
        document.querySelectorAll('[data-tab-target]').forEach(tab => {
            tab.addEventListener('click', () => {
                // Trigger map resize
                setTimeout(() => {
                    if (map) {
                        google.maps.event.trigger(map, 'resize');
                        if (pickupMarker && deliveryMarker) {
                            const bounds = new google.maps.LatLngBounds();
                            bounds.extend(pickupMarker.getPosition());
                            bounds.extend(deliveryMarker.getPosition());
                            map.fitBounds(bounds);
                        }
                    }
                }, 300);
            });
        });
    }

    function updateMapAndRoute() {
        const pickupLat = {{ $booking->pickup_latitude ?? 5.6037 }};
        const pickupLng = {{ $booking->pickup_longitude ?? -0.1870 }};
        const deliveryLat = {{ $booking->delivery_latitude ?? 5.6037 }};
        const deliveryLng = {{ $booking->delivery_longitude ?? -0.1870 }};

        if (!map || isNaN(pickupLat) || isNaN(pickupLng) || isNaN(deliveryLat) || isNaN(deliveryLng)) {
            return;
        }

        // Create pickup marker
        pickupMarker = new google.maps.Marker({
            position: { lat: pickupLat, lng: pickupLng },
            map: map,
            title: 'Pickup Location',
            icon: {
                url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                    <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="16" cy="16" r="12" fill="#10B981" stroke="white" stroke-width="3"/>
                        <text x="16" y="20" text-anchor="middle" fill="white" font-family="Arial" font-size="12" font-weight="bold">P</text>
                    </svg>
                `),
                scaledSize: new google.maps.Size(32, 32),
                anchor: new google.maps.Point(16, 16)
            }
        });

        // Create delivery marker
        deliveryMarker = new google.maps.Marker({
            position: { lat: deliveryLat, lng: deliveryLng },
            map: map,
            title: 'Delivery Location',
            icon: {
                url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                    <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="16" cy="16" r="12" fill="#EF4444" stroke="white" stroke-width="3"/>
                        <text x="16" y="20" text-anchor="middle" fill="white" font-family="Arial" font-size="12" font-weight="bold">D</text>
                    </svg>
                `),
                scaledSize: new google.maps.Size(32, 32),
                anchor: new google.maps.Point(16, 16)
            }
        });

        // Add info windows
        const pickupInfoWindow = new google.maps.InfoWindow({
            content: `
                <div class="p-2">
                    <h4 class="font-bold">Pickup Location</h4>
                    <p class="text-sm">{{ $booking->pickup_address }}</p>
                    <p class="text-xs text-gray-600">{{ $booking->pickup_person_name }}</p>
                </div>
            `
        });

        const deliveryInfoWindow = new google.maps.InfoWindow({
            content: `
                <div class="p-2">
                    <h4 class="font-bold">Delivery Location</h4>
                    <p class="text-sm">{{ $booking->delivery_address }}</p>
                    <p class="text-xs text-gray-600">{{ $booking->receiver_name }}</p>
                </div>
            `
        });

        pickupMarker.addListener('click', () => {
            pickupInfoWindow.open(map, pickupMarker);
        });

        deliveryMarker.addListener('click', () => {
            deliveryInfoWindow.open(map, deliveryMarker);
        });

        // Calculate and display route
        directionsService.route({
            origin: { lat: pickupLat, lng: pickupLng },
            destination: { lat: deliveryLat, lng: deliveryLng },
            travelMode: google.maps.TravelMode.DRIVING
        }, (result, status) => {
            if (status === 'OK') {
                directionsRenderer.setDirections(result);

                // Update distance and duration if not already set
                const route = result.routes[0];
                if (route && route.legs && route.legs.length > 0) {
                    const leg = route.legs[0];
                    const distanceElement = document.getElementById('estimated-distance');
                    const durationElement = document.getElementById('estimated-duration');

                    if (distanceElement && distanceElement.textContent === '-- km') {
                        distanceElement.textContent = leg.distance.text;
                    }
                    if (durationElement && durationElement.textContent === '-- mins') {
                        durationElement.textContent = leg.duration.text;
                    }
                }

                // Fit map to show route
                const bounds = new google.maps.LatLngBounds();
                bounds.extend({ lat: pickupLat, lng: pickupLng });
                bounds.extend({ lat: deliveryLat, lng: deliveryLng });
                map.fitBounds(bounds);
            }
        });
    }

    document.getElementById('status').addEventListener('change', function() {
        const cancellationDiv = document.getElementById('cancellation_reason_div');
        if (this.value === 'cancelled') {
            cancellationDiv.style.display = 'block';
        } else {
            cancellationDiv.style.display = 'none';
        }
    });

    // Payment status switcher functions
    function togglePaymentStatusSwitcher() {
        const switcher = document.getElementById('payment-status-switcher');
        if (switcher.classList.contains('hidden')) {
            switcher.classList.remove('hidden');
        } else {
            switcher.classList.add('hidden');
            // Reset form when hiding
            const form = switcher.querySelector('form');
            if (form) {
                form.reset();
                // Reset payment status to current value
                const currentStatus = '{{ $booking->payment_status }}';
                document.getElementById('payment_status').value = currentStatus;
                // Hide notes div
                document.getElementById('payment_notes_div').classList.add('hidden');
            }
        }
    }

    // Show/hide payment notes based on status selection
    document.addEventListener('DOMContentLoaded', function() {
        const paymentStatusSelect = document.getElementById('payment_status');
        const paymentNotesDiv = document.getElementById('payment_notes_div');

        if (paymentStatusSelect) {
            paymentStatusSelect.addEventListener('change', function() {
                if (this.value === 'failed' || this.value === 'refunded') {
                    paymentNotesDiv.classList.remove('hidden');
                } else {
                    paymentNotesDiv.classList.add('hidden');
                }
            });
        }
    });

    // Auto-refresh for active bookings (Admin view)
    @if(in_array($booking->status, ['confirmed', 'in_progress']))
        let adminAutoRefreshInterval;
        let refreshCount = 0;
        const maxRefreshes = 120; // Stop auto-refresh after 2 hours (120 * 60 seconds)

        function startAdminAutoRefresh() {
            adminAutoRefreshInterval = setInterval(() => {
                refreshCount++;
                if (refreshCount >= maxRefreshes) {
                    stopAdminAutoRefresh();
                    return;
                }

                // Refresh the map and check for updates
                if (typeof updateMapAndRoute === 'function') {
                    updateMapAndRoute();
                }

                // Optionally refresh the entire page every 10 minutes for status updates
                if (refreshCount % 10 === 0) {
                    checkForStatusUpdate();
                }
            }, 60000); // Refresh every 60 seconds
        }

        function stopAdminAutoRefresh() {
            if (adminAutoRefreshInterval) {
                clearInterval(adminAutoRefreshInterval);
                adminAutoRefreshInterval = null;
            }
        }

        function checkForStatusUpdate() {
            fetch(`/api/live-map/booking/{{ $booking->id }}`)
                .then(response => response.json())
                .then(data => {
                    // Check if status has changed
                    const currentStatus = '{{ $booking->status }}';
                    if (data.status && data.status !== currentStatus) {
                        // Reload page if status changed
                        window.location.reload();
                    }
                })
                .catch(error => console.error('Error checking status:', error));
        }

        // Start auto-refresh when page loads
        document.addEventListener('DOMContentLoaded', function() {
            startAdminAutoRefresh();
        });

        // Stop auto-refresh when page is hidden
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                stopAdminAutoRefresh();
            } else {
                startAdminAutoRefresh();
            }
        });

        // Stop auto-refresh when user leaves the page
        window.addEventListener('beforeunload', function() {
            stopAdminAutoRefresh();
        });
    @endif

    function refreshAdminTracking() {
        const refreshIcon = document.getElementById('admin-refresh-icon');

        // Add spinning animation
        refreshIcon.classList.add('fa-spin');

        // Refresh the map
        if (typeof updateMapAndRoute === 'function') {
            updateMapAndRoute();
        }

        // Remove spinning animation after 2 seconds
        setTimeout(() => {
            refreshIcon.classList.remove('fa-spin');
        }, 2000);
    }
</script>
@endsection
