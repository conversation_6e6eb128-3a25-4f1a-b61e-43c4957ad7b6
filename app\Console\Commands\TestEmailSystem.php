<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\Booking;
use App\Services\EmailService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class TestEmailSystem extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:test {type=all : Type of email to test (welcome|booking|status|admin|password|all)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the email system functionality';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $type = $this->argument('type');

        $this->info('🚀 Testing TTAJet Email System...');
        $this->newLine();

        try {
            switch ($type) {
                case 'welcome':
                    $this->testWelcomeEmail();
                    break;
                case 'booking':
                    $this->testBookingConfirmationEmail();
                    break;
                case 'status':
                    $this->testStatusUpdateEmail();
                    break;
                case 'admin':
                    $this->testAdminNotificationEmail();
                    break;
                case 'password':
                    $this->testPasswordResetEmail();
                    break;
                case 'all':
                default:
                    $this->testWelcomeEmail();
                    $this->testBookingConfirmationEmail();
                    $this->testStatusUpdateEmail();
                    $this->testAdminNotificationEmail();
                    $this->testPasswordResetEmail();
                    break;
            }

            $this->newLine();
            $this->info('✅ Email system testing completed!');
            $this->info('📧 Check your email logs for delivery status.');

        } catch (\Exception $e) {
            $this->error('❌ Email system test failed: ' . $e->getMessage());
            Log::error('Email system test failed: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }

    private function testWelcomeEmail()
    {
        $this->info('📧 Testing Welcome Email...');
        
        $user = User::where('role', 'customer')->first();
        if (!$user) {
            $this->warn('⚠️  No customer users found. Creating test user...');
            $user = User::create([
                'name' => 'Test Customer',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'phone_number' => '+1234567890',
                'role' => 'customer',
                'is_approved' => true,
                'is_active' => true,
            ]);
        }

        $result = EmailService::sendWelcomeEmail($user);
        
        if ($result) {
            $this->info("✅ Welcome email sent to: {$user->email}");
        } else {
            $this->warn("⚠️  Welcome email not sent (may be disabled in settings)");
        }
    }

    private function testBookingConfirmationEmail()
    {
        $this->info('📧 Testing Booking Confirmation Email...');
        
        $booking = Booking::with('customer')->first();
        if (!$booking) {
            $this->warn('⚠️  No bookings found. Cannot test booking confirmation email.');
            return;
        }

        $result = EmailService::sendBookingConfirmation($booking);
        
        if ($result) {
            $this->info("✅ Booking confirmation email sent to: {$booking->customer->email}");
        } else {
            $this->warn("⚠️  Booking confirmation email not sent (may be disabled in settings)");
        }
    }

    private function testStatusUpdateEmail()
    {
        $this->info('📧 Testing Status Update Email...');
        
        $booking = Booking::with('customer')->first();
        if (!$booking) {
            $this->warn('⚠️  No bookings found. Cannot test status update email.');
            return;
        }

        $result = EmailService::sendBookingStatusUpdate($booking, 'pending');
        
        if ($result) {
            $this->info("✅ Status update email sent to: {$booking->customer->email}");
        } else {
            $this->warn("⚠️  Status update email not sent (may be disabled in settings)");
        }
    }

    private function testAdminNotificationEmail()
    {
        $this->info('📧 Testing Admin Notification Email...');
        
        $booking = Booking::with('customer')->first();
        if (!$booking) {
            $this->warn('⚠️  No bookings found. Cannot test admin notification email.');
            return;
        }

        $result = EmailService::sendAdminNewBookingNotification($booking);
        
        if ($result) {
            $this->info("✅ Admin notification email sent");
        } else {
            $this->warn("⚠️  Admin notification email not sent (may be disabled in settings)");
        }
    }

    private function testPasswordResetEmail()
    {
        $this->info('📧 Testing Password Reset Email...');
        
        $user = User::where('role', 'customer')->first();
        if (!$user) {
            $this->warn('⚠️  No customer users found. Cannot test password reset email.');
            return;
        }

        $resetUrl = url('/reset-password/test-token?email=' . urlencode($user->email));
        $result = EmailService::sendPasswordResetEmail($user, $resetUrl);
        
        if ($result) {
            $this->info("✅ Password reset email sent to: {$user->email}");
        } else {
            $this->warn("⚠️  Password reset email not sent (may be disabled in settings)");
        }
    }
}
