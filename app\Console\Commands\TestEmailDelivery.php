<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\EmailService;
use Illuminate\Support\Facades\DB;

class TestEmailDelivery extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:test-delivery {email}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test email delivery system';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        
        $this->info('🧪 Testing TTAJet Email Delivery System');
        $this->newLine();
        
        // Check queue status
        try {
            $pendingJobs = DB::table('jobs')->count();
            $this->info("📋 Current queue status: {$pendingJobs} pending jobs");
        } catch (\Exception $e) {
            $this->warn("⚠️  Could not check queue status: " . $e->getMessage());
        }
        
        $this->newLine();
        $this->info("📧 Testing welcome email to: {$email}");
        
        try {
            // Test welcome email
            EmailService::sendWelcomeEmail($email, 'Test User');
            $this->info("✅ Welcome email sent successfully!");
            
            // Check if any new jobs were queued
            $newPendingJobs = DB::table('jobs')->count();
            if ($newPendingJobs > $pendingJobs) {
                $this->warn("⚠️  Email was queued instead of sent immediately");
                $this->info("💡 Run 'php artisan queue:work' to process queued emails");
            } else {
                $this->info("✅ Email was sent immediately (not queued)");
            }
            
        } catch (\Exception $e) {
            $this->error("❌ Failed to send welcome email: " . $e->getMessage());
            return 1;
        }
        
        $this->newLine();
        $this->info('🎉 Email delivery test completed!');
        $this->info('📝 Check the Laravel logs for detailed email sending information');
        
        return 0;
    }
}
