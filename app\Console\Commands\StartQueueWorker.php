<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;

class StartQueueWorker extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'queue:start {--daemon : Run as daemon} {--timeout=60 : Timeout in seconds}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Start the queue worker to process email and other jobs';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Starting TTAJet Queue Worker...');
        $this->newLine();

        // Display current queue configuration
        $this->displayQueueInfo();
        $this->newLine();

        $timeout = $this->option('timeout');
        $daemon = $this->option('daemon');

        if ($daemon) {
            $this->info('📡 Starting queue worker in daemon mode...');
            $this->warn('⚠️  To stop the worker, use Ctrl+C or kill the process');
            $this->newLine();
            
            // Run as daemon
            Artisan::call('queue:work', [
                '--daemon' => true,
                '--timeout' => $timeout,
                '--tries' => 3,
                '--sleep' => 3,
                '--max-jobs' => 1000,
                '--max-time' => 3600, // 1 hour
            ]);
        } else {
            $this->info('⚡ Processing queued jobs (non-daemon mode)...');
            $this->info('💡 Use --daemon flag to run continuously');
            $this->newLine();

            // Process jobs until queue is empty
            $processed = 0;
            while (true) {
                $exitCode = Artisan::call('queue:work', [
                    '--once' => true,
                    '--timeout' => $timeout,
                    '--tries' => 3,
                ]);

                if ($exitCode === 0) {
                    $processed++;
                    $this->info("✅ Processed job #{$processed}");
                } else {
                    break;
                }

                // Check if there are more jobs
                $pendingJobs = \DB::table('jobs')->count();
                if ($pendingJobs === 0) {
                    break;
                }
            }

            if ($processed > 0) {
                $this->info("🎉 Successfully processed {$processed} jobs!");
            } else {
                $this->info('📭 No jobs found in queue');
            }
        }

        return 0;
    }

    /**
     * Display current queue configuration
     */
    private function displayQueueInfo()
    {
        $this->info('📊 Queue Configuration:');
        $this->table(
            ['Setting', 'Value'],
            [
                ['Queue Driver', config('queue.default')],
                ['Queue Connection', config('queue.connections.' . config('queue.default') . '.driver')],
                ['Database Table', config('queue.connections.database.table', 'jobs')],
                ['Failed Jobs Table', config('queue.failed.table', 'failed_jobs')],
            ]
        );

        // Show pending jobs count
        try {
            $pendingJobs = \DB::table('jobs')->count();
            $failedJobs = \DB::table('failed_jobs')->count();
            
            $this->info("📋 Queue Status:");
            $this->line("   • Pending Jobs: {$pendingJobs}");
            $this->line("   • Failed Jobs: {$failedJobs}");
        } catch (\Exception $e) {
            $this->warn('⚠️  Could not retrieve queue status: ' . $e->getMessage());
        }
    }
}
