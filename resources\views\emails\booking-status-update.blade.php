@extends('emails.layout', [
    'title' => 'Booking Status Update',
    'subtitle' => 'Your delivery status has been updated'
])

@section('content')
@php
    $statusConfig = [
        'pending' => ['icon' => '⏳', 'color' => '#f59e0b', 'bg' => '#fef3c7', 'message' => 'Your booking is being processed'],
        'confirmed' => ['icon' => '✅', 'color' => '#10b981', 'bg' => '#d1fae5', 'message' => 'Your booking has been confirmed'],
        'assigned' => ['icon' => '👤', 'color' => '#3b82f6', 'bg' => '#dbeafe', 'message' => 'A rider has been assigned to your delivery'],
        'picked_up' => ['icon' => '📦', 'color' => '#8b5cf6', 'bg' => '#ede9fe', 'message' => 'Your package has been picked up'],
        'in_transit' => ['icon' => '🚚', 'color' => '#f97316', 'bg' => '#fed7aa', 'message' => 'Your package is on the way'],
        'delivered' => ['icon' => '🎉', 'color' => '#059669', 'bg' => '#d1fae5', 'message' => 'Your package has been delivered successfully'],
        'cancelled' => ['icon' => '❌', 'color' => '#dc2626', 'bg' => '#fecaca', 'message' => 'Your booking has been cancelled'],
        'failed' => ['icon' => '⚠️', 'color' => '#dc2626', 'bg' => '#fecaca', 'message' => 'Delivery attempt failed'],
    ];
    
    $currentStatus = $statusConfig[$booking->status] ?? $statusConfig['pending'];
@endphp

<div class="info-card" style="background-color: {{ $currentStatus['bg'] }}; border-color: {{ $currentStatus['color'] }};">
    <h2>{{ $currentStatus['icon'] }} Status Update</h2>
    <p style="color: {{ $currentStatus['color'] }}; font-weight: 600;">{{ $currentStatus['message'] }}</p>
</div>

<p>Dear {{ $customer->name }},</p>

<p>We wanted to update you on the status of your delivery with <strong>TTAJet Courier Service</strong>.</p>

<div class="info-card">
    <h3>📋 Booking Information</h3>
    <table class="data-table">
        <tr>
            <td><strong>Booking ID:</strong></td>
            <td><span style="font-family: monospace; background: #f3f4f6; padding: 2px 6px; border-radius: 4px;">{{ $booking->booking_id }}</span></td>
        </tr>
        <tr>
            <td><strong>Current Status:</strong></td>
            <td>
                <span style="background: {{ $currentStatus['bg'] }}; color: {{ $currentStatus['color'] }}; padding: 4px 12px; border-radius: 12px; font-size: 14px; font-weight: 600;">
                    {{ $currentStatus['icon'] }} {{ ucfirst(str_replace('_', ' ', $booking->status)) }}
                </span>
            </td>
        </tr>
        @if(isset($previousStatus))
        <tr>
            <td><strong>Previous Status:</strong></td>
            <td>{{ ucfirst(str_replace('_', ' ', $previousStatus)) }}</td>
        </tr>
        @endif
        <tr>
            <td><strong>Updated At:</strong></td>
            <td>{{ now()->format('F j, Y \a\t g:i A') }}</td>
        </tr>
    </table>
</div>

@if($booking->status === 'assigned' && isset($rider))
<div class="info-card">
    <h3>👤 Rider Information</h3>
    <table class="data-table">
        <tr>
            <td><strong>Rider Name:</strong></td>
            <td>{{ $rider->name }}</td>
        </tr>
        <tr>
            <td><strong>Phone:</strong></td>
            <td>{{ $rider->phone_number }}</td>
        </tr>
        @if(isset($rider->vehicle_info))
        <tr>
            <td><strong>Vehicle:</strong></td>
            <td>{{ $rider->vehicle_info }}</td>
        </tr>
        @endif
    </table>
    <p style="margin-top: 15px; color: #6b7280;">Your rider will contact you shortly to arrange pickup.</p>
</div>
@endif

@if($booking->status === 'picked_up' && $booking->actual_pickup_time)
<div class="info-card">
    <h3>📦 Pickup Information</h3>
    <p><strong>Picked up at:</strong> {{ \Carbon\Carbon::parse($booking->actual_pickup_time)->format('F j, Y \a\t g:i A') }}</p>
    <p><strong>From:</strong> {{ $booking->pickup_address }}</p>
</div>
@endif

@if($booking->status === 'delivered' && $booking->delivered_at)
<div class="info-card success">
    <h3>🎉 Delivery Completed!</h3>
    <p><strong>Delivered at:</strong> {{ \Carbon\Carbon::parse($booking->delivered_at)->format('F j, Y \a\t g:i A') }}</p>
    <p><strong>To:</strong> {{ $booking->delivery_address }}</p>
    <p><strong>Received by:</strong> {{ $booking->receiver_name }}</p>
    
    @if($booking->final_cost)
    <p><strong>Final Cost:</strong> <span style="color: #F97316; font-weight: 600;">{{ \App\Services\EmailService::formatCurrency($booking->final_cost) }}</span></p>
    @endif
</div>
@endif

@if($booking->status === 'cancelled' && isset($cancellationReason))
<div class="info-card error">
    <h3>❌ Cancellation Details</h3>
    <p><strong>Reason:</strong> {{ $cancellationReason }}</p>
    <p>If you have any questions about this cancellation, please contact our customer service team.</p>
</div>
@endif

@if($booking->status === 'failed' && isset($failureReason))
<div class="info-card error">
    <h3>⚠️ Delivery Attempt Failed</h3>
    <p><strong>Reason:</strong> {{ $failureReason }}</p>
    <p>Our team will contact you to reschedule the delivery. Please ensure someone is available at the delivery address.</p>
</div>
@endif

<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 25px 0;">
    <div class="info-card">
        <h3>📍 Pickup</h3>
        <p>{{ $booking->pickup_address }}</p>
        <p><strong>Contact:</strong> {{ $booking->pickup_person_name }}</p>
    </div>

    <div class="info-card">
        <h3>🎯 Delivery</h3>
        <p>{{ $booking->delivery_address }}</p>
        <p><strong>Receiver:</strong> {{ $booking->receiver_name }}</p>
    </div>
</div>

@if($booking->status !== 'delivered' && $booking->status !== 'cancelled')
<h3>What's Next?</h3>
@if($booking->status === 'pending')
<p>Your booking is being reviewed by our team. You'll receive another update once a rider is assigned.</p>
@elseif($booking->status === 'confirmed')
<p>We're now looking for the best available rider for your delivery. You'll be notified once a rider is assigned.</p>
@elseif($booking->status === 'assigned')
<p>Your assigned rider will contact you shortly to arrange the pickup time and any special instructions.</p>
@elseif($booking->status === 'picked_up')
<p>Your package is now with our rider and on its way to the delivery destination. You can track the progress in real-time.</p>
@elseif($booking->status === 'in_transit')
<p>Your package is currently being delivered. The receiver should be available to accept the delivery.</p>
@endif
@endif

<div class="text-center" style="margin: 30px 0;">
    @if($booking->status !== 'delivered' && $booking->status !== 'cancelled')
    <a href="{{ \App\Services\EmailService::getTrackingUrl($booking->booking_id) }}" class="btn btn-primary">
        📍 Track Your Package
    </a>
    @endif
    <a href="{{ \App\Services\EmailService::getBookingUrl($booking->id) }}" class="btn btn-secondary">
        📋 View Full Details
    </a>
</div>

@if($booking->status === 'delivered')
<div class="info-card">
    <h3>⭐ Rate Your Experience</h3>
    <p>We'd love to hear about your experience with TTAJet! Your feedback helps us improve our service.</p>
    <div class="text-center" style="margin-top: 15px;">
        <a href="{{ route('booking.show', $booking->id) }}#review" class="btn btn-primary">
            ⭐ Leave a Review
        </a>
    </div>
</div>
@endif

<div class="info-card">
    <h3>📞 Need Help?</h3>
    <p>If you have any questions or concerns, please don't hesitate to contact us:</p>
    <ul style="margin: 15px 0; padding-left: 20px;">
        <li>📞 Phone: {{ \App\Services\EmailService::getCompanyInfo()['phone'] }}</li>
        <li>📧 Email: {{ \App\Services\EmailService::getCompanyInfo()['email'] }}</li>
        <li>💬 Reference your Booking ID: <strong>{{ $booking->booking_id }}</strong></li>
    </ul>
</div>

<p>Thank you for choosing TTAJet Courier Service!</p>

<p>Best regards,<br>
<strong>The TTAJet Team</strong></p>
@endsection
